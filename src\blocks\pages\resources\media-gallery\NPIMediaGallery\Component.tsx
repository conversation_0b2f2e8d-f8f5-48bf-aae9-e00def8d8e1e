import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import { Play, Calendar, Eye, ExternalLink, Youtube } from 'lucide-react'

interface MediaItem {
  id: string
  title: string
  description: string
  type: 'video' | 'photo' | 'document'
  thumbnail: string
  url: string
  date: string
  views?: number
  duration?: string
  category: string
}

interface NPIMediaGalleryProps {
  title?: string
  description?: string
  mediaItems?: MediaItem[]
}

export const NPIMediaGalleryBlock: React.FC<NPIMediaGalleryProps> = ({
  title = 'Media Gallery',
  description = "Explore our collection of videos, photos, and media coverage showcasing NPI's impact across Kenya's natural products sector.",
  mediaItems = [
    {
      id: 'npi-documentary-2024',
      title: 'NPI Documentary: Transforming Communities',
      description:
        'A comprehensive documentary showcasing how NPI initiatives are transforming communities across Kenya through natural products development.',
      type: 'video',
      thumbnail: '/assets/background.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2024-01-15',
      views: 15420,
      duration: '12:45',
      category: 'Documentary',
    },
    {
      id: 'aloe-cooperative-story',
      title: 'Baringo Aloe Cooperative Success Story',
      description:
        'Meet the women of Baringo County who transformed their community through traditional aloe vera knowledge.',
      type: 'video',
      thumbnail: '/assets/product 4.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-12-10',
      views: 8750,
      duration: '8:30',
      category: 'Success Stories',
    },
    {
      id: 'ikia-platform-demo',
      title: 'IKIA Platform Demonstration',
      description:
        'Learn how to navigate and use the Indigenous Knowledge and Innovation Assets database platform.',
      type: 'video',
      thumbnail: '/assets/product 2.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2024-01-20',
      views: 5230,
      duration: '15:20',
      category: 'Tutorial',
    },
    {
      id: 'conference-2023-highlights',
      title: 'Kenya Natural Products Conference 2023 Highlights',
      description: 'Key moments and insights from the annual Kenya Natural Products Conference.',
      type: 'video',
      thumbnail: '/assets/product 3.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-11-25',
      views: 12100,
      duration: '6:15',
      category: 'Events',
    },
    {
      id: 'traditional-healers-interview',
      title: 'Traditional Healers Share Their Wisdom',
      description:
        'Interviews with traditional healers from different communities about their knowledge and practices.',
      type: 'video',
      thumbnail: '/assets/product 5.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-10-15',
      views: 9800,
      duration: '18:45',
      category: 'Interviews',
    },
    {
      id: 'youth-entrepreneurs-feature',
      title: 'Young Entrepreneurs Leading Change',
      description:
        'Feature story on young entrepreneurs who are innovating in the natural products sector.',
      type: 'video',
      thumbnail: '/assets/product 6.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-09-20',
      views: 6450,
      duration: '10:30',
      category: 'Youth',
    },
  ],
}) => {
  const getYouTubeVideoId = (url: string): string | null => {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)
    return match ? match[1] : null
  }

  const getYouTubeEmbedUrl = (url: string): string => {
    const videoId = getYouTubeVideoId(url)
    return videoId ? `https://www.youtube.com/embed/${videoId}` : url
  }

  const formatViews = (views: number): string => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`
    }
    return views.toString()
  }

  const categories = Array.from(new Set(mediaItems.map((item) => item.category)))

  return (
    <NPISection>
      <NPISectionHeader>
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 mb-8 justify-center">
        <NPIButton variant="outline" size="sm">
          All
        </NPIButton>
        {categories.map((category) => (
          <NPIButton key={category} variant="ghost" size="sm">
            {category}
          </NPIButton>
        ))}
      </div>

      {/* Featured Video */}
      <div className="mb-12">
        <h3 className="text-2xl font-bold mb-6 font-npi">Featured Video</h3>
        <NPICard className="overflow-hidden">
          <div className="grid lg:grid-cols-2 gap-0">
            <div className="relative aspect-video">
              <iframe
                src={getYouTubeEmbedUrl(mediaItems[0].url)}
                title={mediaItems[0].title}
                className="w-full h-full"
                allowFullScreen
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <Youtube className="w-5 h-5 text-red-600" />
                <span className="text-sm text-muted-foreground font-npi">
                  {mediaItems[0].category}
                </span>
              </div>
              <NPICardTitle className="text-xl mb-3">{mediaItems[0].title}</NPICardTitle>
              <p className="text-muted-foreground mb-4 font-npi">{mediaItems[0].description}</p>

              <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                <span className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {new Date(mediaItems[0].date).toLocaleDateString()}
                </span>
                <span className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  {formatViews(mediaItems[0].views || 0)} views
                </span>
                <span>{mediaItems[0].duration}</span>
              </div>

              <NPIButton asChild variant="primary">
                <Link href={mediaItems[0].url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Watch on YouTube
                </Link>
              </NPIButton>
            </div>
          </div>
        </NPICard>
      </div>

      {/* Video Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mediaItems.slice(1).map((item) => (
          <NPICard
            key={item.id}
            className="overflow-hidden hover:shadow-lg transition-shadow duration-300"
          >
            <div className="relative aspect-video group cursor-pointer">
              <Image
                src={item.thumbnail}
                alt={item.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors duration-300" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-16 h-16 bg-[#25718A] rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Play className="w-8 h-8 text-white ml-1" />
                </div>
              </div>
              <div
                className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 text-xs"
                style={{ borderRadius: '0' }}
              >
                {item.duration}
              </div>
              <div className="absolute top-2 left-2">
                <span
                  className="bg-[#8A3E25] text-white px-2 py-1 text-xs font-medium"
                  style={{ borderRadius: '0' }}
                >
                  {item.category}
                </span>
              </div>
            </div>

            <NPICardHeader>
              <NPICardTitle className="text-lg leading-tight line-clamp-2">
                {item.title}
              </NPICardTitle>
            </NPICardHeader>

            <NPICardContent>
              <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-2 font-npi">
                {item.description}
              </p>

              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {new Date(item.date).toLocaleDateString()}
                </span>
                <span className="flex items-center gap-1">
                  <Eye className="w-3 h-3" />
                  {formatViews(item.views || 0)}
                </span>
              </div>
            </NPICardContent>
          </NPICard>
        ))}
      </div>

      {/* YouTube Channel CTA */}
      <div className="mt-12 text-center">
        <NPICard className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <NPICardContent className="p-8">
            <Youtube className="w-12 h-12 text-red-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold mb-4 font-npi">Subscribe to Our YouTube Channel</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto font-npi">
              Stay updated with the latest videos, documentaries, and stories from Kenya&apos;s
              natural products sector. Subscribe to never miss our content!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <NPIButton asChild size="lg" className="bg-red-600 hover:bg-red-700">
                <Link
                  href="https://youtube.com/@npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Youtube className="w-5 h-5 mr-2" />
                  Subscribe Now
                </Link>
              </NPIButton>
              <NPIButton asChild size="lg" variant="outline">
                <Link href="/media/all-videos">View All Videos</Link>
              </NPIButton>
            </div>
          </NPICardContent>
        </NPICard>
      </div>
    </NPISection>
  )
}
