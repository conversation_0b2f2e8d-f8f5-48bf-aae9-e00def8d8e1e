'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { Quote, MapPin, TrendingUp } from 'lucide-react'

interface SuccessStory {
  title: string
  summary: string
  image: string
  location: string
  category: string
  impact: string
  testimonial?: {
    quote: string
    author: string
    role: string
  }
  link: string
}

interface NPISuccessStoriesProps {
  title?: string
  description?: string
  stories?: SuccessStory[]
}

export const NPISuccessStoriesBlock: React.FC<NPISuccessStoriesProps> = ({
  title = 'Success Stories',
  description = 'Real impact, real change. Discover how our initiatives are transforming lives and communities across Kenya.',
  stories = [
    {
      title: 'Aloe Vera Cooperative Transforms Rural Economy',
      summary:
        "A women's cooperative in Baringo County successfully commercialized traditional aloe vera products, increasing household incomes by 300% and creating 150 jobs.",
      image: '/assets/product 1.jpg',
      location: 'Baringo County',
      category: 'Community-Led Innovation',
      impact: '150 jobs created, 300% income increase',
      testimonial: {
        quote:
          'NPI helped us turn our traditional knowledge into a thriving business. Now our children can go to school and our community has hope.',
        author: 'Mary Chepkemoi',
        role: 'Cooperative Chairwoman',
      },
      link: '/success-stories/aloe-cooperative',
    },
    {
      title: 'Youth-Led Moringa Processing Enterprise',
      summary:
        'Young entrepreneurs in Turkana developed a sustainable moringa processing facility, creating nutritious products while preserving traditional knowledge.',
      image: '/assets/product 2.jpg',
      location: 'Turkana County',
      category: 'Youth Empowerment',
      impact: '50 youth employed, 200 farmers supported',
      testimonial: {
        quote:
          "We learned to combine our ancestors' wisdom with modern technology. Now we're exporting moringa products across East Africa.",
        author: 'John Ekale',
        role: 'Youth Group Leader',
      },
      link: '/success-stories/moringa-youth',
    },
    {
      title: 'Traditional Medicine IP Protection Success',
      summary:
        'Maasai community successfully registered traditional medicine formulations, securing intellectual property rights and establishing sustainable revenue streams.',
      image: '/assets/product 3.jpg',
      location: 'Kajiado County',
      category: 'IP Registration',
      impact: '5 patents registered, community rights protected',
      testimonial: {
        quote:
          'Our traditional knowledge is now protected by law. We can share it with the world while ensuring our community benefits.',
        author: 'Elder Joseph Sankale',
        role: 'Traditional Healer',
      },
      link: '/success-stories/maasai-ip',
    },
  ],
}) => {
  return (
    <NPISection size="sm" className="bg-[#EFE3BA] relative overflow-hidden">
      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="inline-flex items-center px-4 py-2 bg-[#8A3E25] text-white text-sm font-semibold mb-3"
          >
            <TrendingUp className="w-4 h-4 mr-3" />
            Success Stories
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-2 text-black font-bold text-2xl lg:text-3xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <NPISectionDescription className="font-light leading-[1.6] text-[#725242] max-w-xl mx-auto text-sm lg:text-base">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        <div className="grid lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {stories.map((story, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.8,
                delay: index * 0.2,
                type: 'spring',
                stiffness: 80,
              }}
              whileHover={{ y: -8 }}
            >
              <NPICard
                className={`overflow-hidden shadow-lg border-2 transition-all duration-300 hover:shadow-xl group h-full hover:scale-[1.03] hover:-translate-y-1 ${
                  index % 3 === 0
                    ? 'bg-white border-[#8A3E25] hover:border-[#25718A]'
                    : index % 3 === 1
                      ? 'bg-[#EFE3BA] border-[#725242] hover:border-[#8A3E25]'
                      : 'bg-white border-[#25718A] hover:border-[#8A3E25]'
                }`}
              >
                {/* Square aspect ratio container */}
                <div className="relative w-full aspect-square">
                  <Image
                    src={story.image}
                    alt={story.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-black/40" />
                  <div className="absolute top-3 right-3">
                    <motion.div
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ delay: 0.5, type: 'spring' }}
                      className="px-3 py-1 text-xs font-bold text-white bg-[#8A3E25] shadow-lg"
                    >
                      {story.category}
                    </motion.div>
                  </div>
                  <div className="absolute bottom-3 left-3 right-3">
                    <div className="flex items-center text-white/90 text-xs font-light mb-1">
                      <MapPin className="w-3 h-3 mr-2" />
                      {story.location}
                    </div>
                    <div className="flex items-center text-white text-xs font-semibold">
                      <TrendingUp className="w-3 h-3 mr-2" />
                      {story.impact}
                    </div>
                  </div>
                </div>

                <NPICardHeader className="p-4">
                  <NPICardTitle
                    className={`text-lg font-bold leading-tight mb-2 transition-colors ${
                      index % 3 === 0 ? 'text-black' : index % 3 === 1 ? 'text-black' : 'text-black'
                    }`}
                  >
                    {story.title}
                  </NPICardTitle>
                </NPICardHeader>

                <NPICardContent className="px-4 pb-3">
                  <p
                    className={`leading-[1.5] mb-4 font-light text-sm ${
                      index % 3 === 0
                        ? 'text-[#725242]'
                        : index % 3 === 1
                          ? 'text-[#725242]'
                          : 'text-[#725242]'
                    }`}
                  >
                    {story.summary}
                  </p>

                  {story.testimonial && (
                    <motion.blockquote
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="border-l-2 border-[#25718A] pl-3 bg-[#EFE3BA]/50 p-3 relative"
                    >
                      <Quote className="absolute top-1 right-1 w-4 h-4 text-[#25718A]/50" />
                      <p
                        className={`mb-2 italic font-light leading-[1.4] text-xs ${
                          index % 3 === 0
                            ? 'text-[#725242]'
                            : index % 3 === 1
                              ? 'text-[#725242]'
                              : 'text-[#725242]'
                        }`}
                      >
                        &ldquo;{story.testimonial.quote}&rdquo;
                      </p>
                      <footer
                        className={`text-xs ${
                          index % 3 === 0
                            ? 'text-[#725242]'
                            : index % 3 === 1
                              ? 'text-[#725242]'
                              : 'text-[#725242]'
                        }`}
                      >
                        <strong className="font-semibold text-[#25718A]">
                          {story.testimonial.author}
                        </strong>
                        , {story.testimonial.role}
                      </footer>
                    </motion.blockquote>
                  )}
                </NPICardContent>

                <NPICardFooter className="p-4 pt-0">
                  <NPIButton
                    asChild
                    className="w-full bg-[#25718A] text-white hover:bg-[#8A3E25] font-medium transition-all duration-300"
                  >
                    <Link href={story.link}>Read Full Story</Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-8"
        >
          <NPIButton
            asChild
            size="lg"
            className="bg-[#8A3E25] hover:bg-[#25718A] text-white font-bold px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Link href="/success-stories">View All Success Stories</Link>
          </NPIButton>
        </motion.div>
      </div>
    </NPISection>
  )
}
