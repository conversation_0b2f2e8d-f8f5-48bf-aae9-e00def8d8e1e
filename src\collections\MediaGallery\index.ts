import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const MediaGallery: CollectionConfig = {
  slug: 'media-gallery',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'type', 'category', 'featured', 'createdAt'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Media Item',
    plural: 'Media Gallery',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The media item title',
      },
    },
    {
      name: 'description',
      type: 'richText',
      admin: {
        description: 'Detailed description of the media item',
      },
    },
    {
      name: 'caption',
      type: 'textarea',
      maxLength: 200,
      admin: {
        description: 'Brief caption for the media item (max 200 characters)',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Image', value: 'image' },
        { label: 'Video', value: 'video' },
        { label: 'Audio', value: 'audio' },
        { label: 'Document', value: 'document' },
        { label: 'Infographic', value: 'infographic' },
        { label: 'Presentation', value: 'presentation' },
      ],
    },
    {
      name: 'media',
      type: 'upload',
      relationTo: 'media',
      required: true,
      admin: {
        description: 'The main media file',
      },
    },
    {
      name: 'thumbnail',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Custom thumbnail (auto-generated if not provided)',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Events', value: 'events' },
        { label: 'Projects', value: 'projects' },
        { label: 'Community Stories', value: 'community-stories' },
        { label: 'Training & Workshops', value: 'training-workshops' },
        { label: 'Partnerships', value: 'partnerships' },
        { label: 'Research & Innovation', value: 'research-innovation' },
        { label: 'Cultural Heritage', value: 'cultural-heritage' },
        { label: 'Natural Products', value: 'natural-products' },
        { label: 'Leadership & Governance', value: 'leadership-governance' },
        { label: 'Marketing & Promotion', value: 'marketing-promotion' },
      ],
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'county',
          type: 'relationship',
          relationTo: 'counties',
        },
        {
          name: 'specificLocation',
          type: 'text',
          dbName: 'specific_loc',
        },
        {
          name: 'coordinates',
          type: 'group',
          fields: [
            {
              name: 'latitude',
              type: 'number',
            },
            {
              name: 'longitude',
              type: 'number',
            },
          ],
        },
      ],
    },
    {
      name: 'dateCreated',
      type: 'date',
      required: true,
      admin: {
        description: 'When the media was originally created/captured',
      },
    },
    {
      name: 'event',
      type: 'relationship',
      relationTo: 'events',
      admin: {
        description: 'Related event if applicable',
      },
    },
    {
      name: 'project',
      type: 'relationship',
      relationTo: 'projects',
      admin: {
        description: 'Related project if applicable',
      },
    },
    {
      name: 'credits',
      type: 'group',
      fields: [
        {
          name: 'photographer',
          type: 'text',
        },
        {
          name: 'videographer',
          type: 'text',
        },
        {
          name: 'organization',
          type: 'text',
        },
        {
          name: 'copyright',
          type: 'text',
        },
        {
          name: 'license',
          type: 'select',
          options: [
            { label: 'All Rights Reserved', value: 'all-rights-reserved' },
            { label: 'Creative Commons CC BY', value: 'cc-by' },
            { label: 'Creative Commons CC BY-SA', value: 'cc-by-sa' },
            { label: 'Creative Commons CC BY-NC', value: 'cc-by-nc' },
            { label: 'Creative Commons CC BY-NC-SA', value: 'cc-by-nc-sa' },
            { label: 'Public Domain', value: 'public-domain' },
          ],
          defaultValue: 'all-rights-reserved',
        },
      ],
    },
    {
      name: 'technical',
      type: 'group',
      fields: [
        {
          name: 'dimensions',
          type: 'group',
          fields: [
            {
              name: 'width',
              type: 'number',
            },
            {
              name: 'height',
              type: 'number',
            },
          ],
        },
        {
          name: 'duration',
          type: 'text',
          admin: {
            description: 'Duration for video/audio files (e.g., "2:30")',
          },
        },
        {
          name: 'fileSize',
          type: 'text',
          admin: {
            description: 'File size (e.g., "2.5 MB")',
          },
        },
        {
          name: 'format',
          type: 'text',
          admin: {
            description: 'File format (e.g., "JPEG", "MP4", "PDF")',
          },
        },
        {
          name: 'quality',
          type: 'select',
          options: [
            { label: 'Low', value: 'low' },
            { label: 'Medium', value: 'medium' },
            { label: 'High', value: 'high' },
            { label: '4K/Ultra High', value: 'ultra-high' },
          ],
        },
      ],
    },
    {
      name: 'accessibility',
      type: 'group',
      fields: [
        {
          name: 'altText',
          type: 'text',
          admin: {
            description: 'Alternative text for screen readers',
          },
        },
        {
          name: 'transcript',
          type: 'richText',
          admin: {
            description: 'Transcript for video/audio content',
          },
        },
        {
          name: 'subtitles',
          type: 'upload',
          relationTo: 'media',
          admin: {
            description: 'Subtitle file for videos',
          },
        },
      ],
    },
    {
      name: 'usage',
      type: 'group',
      fields: [
        {
          name: 'allowDownload',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'allowEmbedding',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'commercialUse',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'attribution',
          type: 'text',
          admin: {
            description: 'Required attribution text',
          },
        },
      ],
    },
    {
      name: 'analytics',
      type: 'group',
      fields: [
        {
          name: 'viewCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'downloadCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'shareCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'lastViewed',
          type: 'date',
          admin: {
            readOnly: true,
          },
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this media item in galleries and showcases',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this media item visible to the public',
      },
    },
    ...slugField(),
  ],
}

export default MediaGallery
