import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import { ResourcesHero } from '@/components/ResourcesHero'

export const metadata: Metadata = {
  title: 'Resources & Publications - Natural Products Industry Initiative',
  description:
    'Access comprehensive resources, research publications, policy documents, and educational materials supporting natural products development and traditional knowledge preservation in Kenya.',
}

const resourcesPageLayout = [
  {
    blockType: 'npiResourcesLibrary' as const,
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Knowledge Sharing Impact',
    variant: 'secondary',
  },
]

export default function ResourcesPage() {
  return (
    <article className="min-h-screen">
      <ResourcesHero />
      <main className="relative">
        {resourcesPageLayout.map((block, index) => (
          <section
            key={index}
            className={`
              ${index === 0 ? '' : '-mt-1'}
              relative
              ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
            `}
          >
            <RenderBlocks blocks={[block]} />
          </section>
        ))}
      </main>
    </article>
  )
}
