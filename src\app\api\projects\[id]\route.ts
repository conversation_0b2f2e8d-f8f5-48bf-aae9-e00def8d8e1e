import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

type RouteParams = {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // Fetch single project by ID
    const result = await payload.findByID({
      collection: 'projects',
      id,
    })

    return NextResponse.json({
      success: true,
      project: result,
    })
  } catch (error) {
    console.error('Projects GET by ID error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to fetch project',
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params
    const body = await request.json()

    // Update project by ID
    const result = await payload.update({
      collection: 'projects',
      id,
      data: body,
    })

    return NextResponse.json({
      success: true,
      message: 'Project updated successfully',
      project: result,
    })
  } catch (error) {
    console.error('Projects PUT by ID error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to update project',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // Delete project by ID
    await payload.delete({
      collection: 'projects',
      id,
    })

    return NextResponse.json({
      success: true,
      message: 'Project deleted successfully',
    })
  } catch (error) {
    console.error('Projects DELETE by ID error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to delete project',
      },
      { status: 500 },
    )
  }
}
