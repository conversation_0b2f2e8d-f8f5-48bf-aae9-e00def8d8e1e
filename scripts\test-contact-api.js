#!/usr/bin/env node

/**
 * Contact API Test Script
 * 
 * This script tests the contact form API endpoint to ensure it's working
 * properly and data is being saved to the database.
 */

const fetch = require('node-fetch')

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'

async function testContactAPI() {
  console.log('🔍 Testing Contact Form API...\n')

  try {
    // Test 1: Health check
    console.log('🏥 Testing health endpoint...')
    const healthResponse = await fetch(`${API_BASE_URL}/api/health`)
    const healthData = await healthResponse.json()
    
    if (healthData.success) {
      console.log('✅ Health check passed')
      console.log(`   Database: ${healthData.database.type}`)
      console.log(`   Host: ${healthData.database.host}`)
      console.log(`   Connected: ${healthData.database.connected}`)
      console.log(`   Users: ${healthData.database.userCount}\n`)
    } else {
      throw new Error('Health check failed')
    }

    // Test 2: Submit contact form
    console.log('📧 Testing contact form submission...')
    const contactData = {
      name: 'API Test User',
      email: '<EMAIL>',
      organization: 'NPI Testing Department',
      subject: 'API Endpoint Test',
      category: 'general',
      message: 'This is a test message to verify the contact form API endpoint is working correctly.',
      priority: 'medium'
    }

    const contactResponse = await fetch(`${API_BASE_URL}/api/contact-submissions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(contactData),
    })

    const contactResult = await contactResponse.json()

    if (contactResponse.ok && contactResult.success) {
      console.log('✅ Contact form submission successful')
      console.log(`   Submission ID: ${contactResult.submissionId}`)
      console.log(`   Status: ${contactResult.status}`)
      console.log(`   Message: ${contactResult.message}\n`)
    } else {
      console.error('❌ Contact form submission failed')
      console.error(`   Status: ${contactResponse.status}`)
      console.error(`   Error: ${contactResult.error || contactResult.message}`)
      throw new Error('Contact form submission failed')
    }

    // Test 3: Test with missing required fields
    console.log('🚫 Testing validation with missing fields...')
    const invalidData = {
      name: 'Test User',
      // Missing email, subject, category, message
    }

    const invalidResponse = await fetch(`${API_BASE_URL}/api/contact-submissions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidData),
    })

    const invalidResult = await invalidResponse.json()

    if (invalidResponse.status === 400) {
      console.log('✅ Validation working correctly')
      console.log(`   Error message: ${invalidResult.message}\n`)
    } else {
      console.warn('⚠️ Validation might not be working as expected')
    }

    // Test 4: Test other public endpoints
    console.log('📊 Testing other public endpoints...')
    const endpoints = [
      '/api/projects',
      '/api/success-stories',
      '/api/resources',
      '/api/news',
      '/api/partnerships',
      '/api/counties',
    ]

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`)
        const data = await response.json()
        
        if (response.ok) {
          console.log(`  ✅ ${endpoint}: ${data.docs?.length || 0} items`)
        } else {
          console.log(`  ❌ ${endpoint}: ${response.status} - ${data.error || 'Unknown error'}`)
        }
      } catch (error) {
        console.log(`  ❌ ${endpoint}: Network error - ${error.message}`)
      }
    }

    console.log('\n🎉 API tests completed successfully!')
    console.log('\n📋 Summary:')
    console.log('  ✅ Health endpoint: Working')
    console.log('  ✅ Contact form API: Working')
    console.log('  ✅ Form validation: Working')
    console.log('  ✅ Database integration: Working')
    console.log('  ✅ Public endpoints: Accessible')

  } catch (error) {
    console.error('❌ API test failed:', error.message)
    console.error('\n🔧 Troubleshooting tips:')
    console.error('  1. Make sure the development server is running (npm run dev)')
    console.error('  2. Check that the server is accessible at', API_BASE_URL)
    console.error('  3. Verify database connection is working')
    console.error('  4. Check server logs for detailed error messages')
    
    process.exit(1)
  }
}

// Run the test
testContactAPI()
