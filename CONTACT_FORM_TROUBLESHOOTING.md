# Contact Form Troubleshooting Guide

## Issue: "Sorry, there was an error sending your message. Please try again or contact us directly."

I've identified and fixed the main issue with the contact form. The problem was that the API endpoints were configured in PayloadCMS but the actual route files were missing.

## What I Fixed

### ✅ Created Missing API Route Files
The contact form was trying to call `/api/contact-submissions` but this route didn't exist. I've now created:

- `src/app/api/contact-submissions/route.ts` - Contact form submission endpoint
- `src/app/api/projects/route.ts` - Projects API endpoint  
- `src/app/api/counties/route.ts` - Counties API endpoint
- `src/app/api/success-stories/route.ts` - Success stories endpoint
- `src/app/api/resources/route.ts` - Resources endpoint
- `src/app/api/news/route.ts` - News endpoint
- `src/app/api/partnerships/route.ts` - Partnerships endpoint

### ✅ Enhanced Contact Form Implementation
The contact form now has:
- Proper API integration with error handling
- Visual success/error feedback
- Form validation
- Automatic form reset after successful submission

## How to Test the Fix

### Option 1: Quick Diagnosis (Recommended)
```bash
npm run diagnose:contact
```
This will test all aspects of the contact form and tell you exactly what's working or broken.

### Option 2: Manual Testing
1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Test the contact form**:
   - Visit: http://localhost:3000/contact
   - Fill out all required fields (name, email, subject, category, message)
   - Click "Send Message"
   - You should see a green success message

3. **Verify in admin panel**:
   - Visit: http://localhost:3000/admin
   - Log in with your admin credentials
   - Go to "Contact Submissions" collection
   - Your submission should appear there

### Option 3: Test API Directly
```bash
# Test the contact form API endpoint
curl -X POST http://localhost:3000/api/contact-submissions \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Test Subject",
    "category": "general",
    "message": "This is a test message"
  }'
```

## Common Issues and Solutions

### 1. Server Not Running
**Error**: Network connection failed
**Solution**: Make sure the development server is running:
```bash
npm run dev
```

### 2. Database Connection Issues
**Error**: Database connection failed
**Solution**: 
- Check your `.env.local` file has the correct `DATABASE_URI`
- Verify MongoDB Atlas cluster is running
- Check IP whitelist in MongoDB Atlas

### 3. Missing Environment Variables
**Error**: Configuration errors
**Solution**: Ensure these variables are set in `.env.local`:
```env
DATABASE_URI=mongodb+srv://connect:<EMAIL>/?retryWrites=true&w=majority&appName=npi
PAYLOAD_SECRET=your-super-secure-secret-key-min-32-chars-here
NEXT_PUBLIC_API_URL=http://localhost:3000
```

### 4. Form Validation Errors
**Error**: "Name, email, subject, message, and category are required"
**Solution**: Make sure all required fields are filled out:
- Full Name (required)
- Email Address (required)
- Subject (required)
- Category (required)
- Message (required)

### 5. PayloadCMS Not Initialized
**Error**: PayloadCMS initialization failed
**Solution**: 
```bash
# Rebuild the application
npm run build
npm run dev
```

## Debugging Steps

### 1. Check Browser Console
1. Open browser developer tools (F12)
2. Go to Console tab
3. Submit the form and look for JavaScript errors
4. Check Network tab for failed API requests

### 2. Check Server Logs
1. Look at the terminal where `npm run dev` is running
2. Submit the form and watch for error messages
3. Look for database connection errors or API errors

### 3. Test Individual Components
```bash
# Test database connection
npm run test:db

# Test all API endpoints
npm run verify:database

# Diagnose contact form specifically
npm run diagnose:contact
```

## Expected Behavior

### ✅ Successful Submission
1. User fills out form completely
2. Clicks "Send Message" button
3. Button shows "Sending..." with spinner
4. Green success message appears: "Thank you for your message! We will get back to you soon."
5. Form fields are cleared
6. Submission appears in admin panel

### ❌ Failed Submission
1. Red error message appears with specific error details
2. Form data is preserved (not cleared)
3. User can try again or contact directly

## Contact Form Data Flow

```
User Form → /api/contact-submissions → PayloadCMS → MongoDB → Admin Panel
```

1. **User submits form** on contact page
2. **Frontend calls** `/api/contact-submissions` with POST request
3. **API route** validates data and calls PayloadCMS
4. **PayloadCMS** saves to `contact-submissions` collection
5. **MongoDB** stores the data permanently
6. **Admin can view** submissions in CMS admin panel

## Next Steps

1. **Test the contact form** to confirm it's working
2. **Check admin panel** to see submissions
3. **Monitor for any remaining issues**

The contact form should now be fully functional with proper database integration!
