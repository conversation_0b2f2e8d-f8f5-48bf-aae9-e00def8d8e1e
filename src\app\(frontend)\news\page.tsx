import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'News & Media - Natural Products Industry Initiative',
  description:
    "Stay informed about the latest developments, achievements, and insights from Kenya's natural products sector. Read news articles, press releases, and media coverage.",
}

const newsPageLayout = [
  {
    blockType: 'npiNewsHero' as const,
    title: 'News & Media',
    backgroundImage: '/assets/background.jpg',
  },
  {
    blockType: 'npiNewsListing' as const,
    id: 'latest-news',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Media Reach & Impact',
    variant: 'secondary',
  },
]

export default function NewsPage() {
  return (
    <article className="min-h-screen">
      {newsPageLayout.map((block, index) => (
        <section
          key={index}
          className={`
            ${index === 0 ? '' : '-mt-1'}
            relative
            ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
          `}
        >
          <RenderBlocks blocks={[block]} />
        </section>
      ))}
    </article>
  )
}
