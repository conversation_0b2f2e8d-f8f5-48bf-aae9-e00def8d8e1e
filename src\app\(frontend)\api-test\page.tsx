'use client'

import React, { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
// Simple Badge component inline
const Badge = ({
  children,
  className,
  variant,
}: {
  children: React.ReactNode
  className?: string
  variant?: string
}) => {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium'
  const variantClasses =
    variant === 'destructive' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
  return <span className={`${baseClasses} ${variantClasses} ${className || ''}`}>{children}</span>
}
import { Loader2, CheckCircle, XCircle, Send } from 'lucide-react'

export default function ApiTestPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any[]>([])
  const [projectData, setProjectData] = useState({
    title: 'Test Project via UI',
    description: {
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: 'A test project created through the API test interface',
                type: 'text',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1,
      },
    },
    summary: 'A test project created through the API test interface for validation',
    category: 'community-empowerment',
    pillar: 'community-innovation',
    status: 'active',
    timeline: {
      startDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
      endDate: '',
      duration: '',
    },
    image: '', // Optional for testing - leave empty or provide valid media ID
    featured: false,
    published: true,
  })

  const addResult = (test: string, success: boolean, data: any, error?: string) => {
    setResults((prev) => [
      ...prev,
      {
        id: Date.now(),
        test,
        success,
        data,
        error,
        timestamp: new Date().toISOString(),
      },
    ])
  }

  const testContactSubmissionsAPI = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/contact-submissions')
      const data = await response.json()

      if (response.ok) {
        addResult('GET /api/contact-submissions', true, {
          status: response.status,
          totalSubmissions: data.totalSubmissions,
          submissions: data.submissions?.length || 0,
        })
      } else {
        addResult('GET /api/contact-submissions', false, data, `HTTP ${response.status}`)
      }
    } catch (error) {
      addResult(
        'GET /api/contact-submissions',
        false,
        null,
        error instanceof Error ? error.message : 'Unknown error',
      )
    }
  }

  const testProjectsGetAPI = async () => {
    try {
      const response = await fetch('/api/projects')
      const data = await response.json()

      if (response.ok) {
        addResult('GET /api/projects', true, {
          status: response.status,
          totalProjects: data.totalProjects,
          projects: data.projects?.length || 0,
        })
      } else {
        addResult('GET /api/projects', false, data, `HTTP ${response.status}`)
      }
    } catch (error) {
      addResult(
        'GET /api/projects',
        false,
        null,
        error instanceof Error ? error.message : 'Unknown error',
      )
    }
  }

  const testProjectsPostAPI = async () => {
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData),
      })
      const data = await response.json()

      if (response.ok) {
        addResult('POST /api/projects', true, {
          status: response.status,
          message: data.message,
          projectId: data.project?.id,
        })
      } else {
        addResult('POST /api/projects', false, data, `HTTP ${response.status}`)
      }
    } catch (error) {
      addResult(
        'POST /api/projects',
        false,
        null,
        error instanceof Error ? error.message : 'Unknown error',
      )
    }
  }

  const runAllTests = async () => {
    setLoading(true)
    setResults([])

    await testContactSubmissionsAPI()
    await testProjectsGetAPI()
    await testProjectsPostAPI()

    setLoading(false)
  }

  const clearResults = () => {
    setResults([])
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">API Test Dashboard</h1>
          <p className="text-gray-600">Test API endpoints to verify functionality</p>
        </div>

        {/* Test Controls */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Quick Tests</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={testContactSubmissionsAPI} disabled={loading} className="w-full">
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <Send className="w-4 h-4 mr-2" />
                )}
                Test Contact Submissions API
              </Button>

              <Button
                onClick={testProjectsGetAPI}
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <Send className="w-4 h-4 mr-2" />
                )}
                Test Projects GET API
              </Button>

              <Button
                onClick={testProjectsPostAPI}
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <Send className="w-4 h-4 mr-2" />
                )}
                Test Projects POST API
              </Button>

              <div className="flex space-x-2">
                <Button
                  onClick={runAllTests}
                  disabled={loading}
                  className="flex-1"
                  variant="default"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : (
                    <Send className="w-4 h-4 mr-2" />
                  )}
                  Run All Tests
                </Button>

                <Button onClick={clearResults} disabled={loading} variant="outline">
                  Clear
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Test Data</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Title</label>
                <Input
                  value={projectData.title}
                  onChange={(e) => setProjectData((prev) => ({ ...prev, title: e.target.value }))}
                  placeholder="Project title"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Summary</label>
                <Textarea
                  value={projectData.summary}
                  onChange={(e) => setProjectData((prev) => ({ ...prev, summary: e.target.value }))}
                  placeholder="Project summary (max 300 characters)"
                  rows={3}
                  maxLength={300}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Category</label>
                  <select
                    value={projectData.category}
                    onChange={(e) =>
                      setProjectData((prev) => ({ ...prev, category: e.target.value }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="knowledge-preservation">Knowledge Preservation</option>
                    <option value="community-empowerment">Community Empowerment</option>
                    <option value="capacity-building">Capacity Building</option>
                    <option value="research-development">Research Development</option>
                    <option value="policy-advocacy">Policy Advocacy</option>
                    <option value="market-development">Market Development</option>
                    <option value="technology-innovation">Technology Innovation</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Pillar</label>
                  <select
                    value={projectData.pillar}
                    onChange={(e) =>
                      setProjectData((prev) => ({ ...prev, pillar: e.target.value }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="indigenous-knowledge">Indigenous Knowledge</option>
                    <option value="community-innovation">Community Innovation</option>
                    <option value="capacity-building">Capacity Building</option>
                    <option value="market-development">Market Development</option>
                    <option value="policy-framework">Policy Framework</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Status</label>
                  <select
                    value={projectData.status}
                    onChange={(e) =>
                      setProjectData((prev) => ({ ...prev, status: e.target.value }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="planning">Planning</option>
                    <option value="active">Active</option>
                    <option value="completed">Completed</option>
                    <option value="on-hold">On Hold</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Image ID</label>
                  <Input
                    value={projectData.image}
                    onChange={(e) => setProjectData((prev) => ({ ...prev, image: e.target.value }))}
                    placeholder="Media ID for project image"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Timeline (Required)</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Start Date *</label>
                    <Input
                      type="date"
                      value={projectData.timeline.startDate}
                      onChange={(e) =>
                        setProjectData((prev) => ({
                          ...prev,
                          timeline: { ...prev.timeline, startDate: e.target.value },
                        }))
                      }
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">End Date</label>
                    <Input
                      type="date"
                      value={projectData.timeline.endDate}
                      onChange={(e) =>
                        setProjectData((prev) => ({
                          ...prev,
                          timeline: { ...prev.timeline, endDate: e.target.value },
                        }))
                      }
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Duration</label>
                  <Input
                    value={projectData.timeline.duration}
                    onChange={(e) =>
                      setProjectData((prev) => ({
                        ...prev,
                        timeline: { ...prev.timeline, duration: e.target.value },
                      }))
                    }
                    placeholder="e.g., 18 months, 2022-2025"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Results */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            {results.length === 0 ? (
              <div className="text-center py-8">
                <Send className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">
                  No tests run yet. Click a test button to get started.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {results.map((result) => (
                  <div key={result.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {result.success ? (
                          <CheckCircle className="w-5 h-5 text-green-600" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-600" />
                        )}
                        <h3 className="font-semibold text-gray-900">{result.test}</h3>
                      </div>
                      <Badge variant={result.success ? 'default' : 'destructive'}>
                        {result.success ? 'Success' : 'Failed'}
                      </Badge>
                    </div>

                    {result.error && (
                      <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded">
                        <p className="text-sm text-red-700 font-medium">Error:</p>
                        <p className="text-sm text-red-600">{result.error}</p>
                      </div>
                    )}

                    {result.data && (
                      <div className="mb-3 p-3 bg-gray-50 border border-gray-200 rounded">
                        <p className="text-sm text-gray-700 font-medium mb-2">Response Data:</p>
                        <pre className="text-xs text-gray-600 overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </div>
                    )}

                    <div className="text-xs text-gray-500">
                      {new Date(result.timestamp).toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
