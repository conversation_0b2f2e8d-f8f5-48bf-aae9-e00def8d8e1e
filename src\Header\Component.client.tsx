'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import type { Header } from '@/payload-types'

import { Logo } from '@/components/Logo/Logo'
import { HeaderNav } from './Nav'

interface HeaderClientProps {
  data: Header
}

export const HeaderClient: React.FC<HeaderClientProps> = ({ data }) => {
  /* Storing the value in a useState to avoid hydration errors */
  const [theme, setTheme] = useState<string | null>(null)
  const [isScrolled, setIsScrolled] = useState(false)
  const { headerTheme, setHeaderTheme } = useHeaderTheme()
  const pathname = usePathname()

  useEffect(() => {
    setHeaderTheme(null)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname])

  useEffect(() => {
    if (headerTheme && headerTheme !== theme) setTheme(headerTheme)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [headerTheme])

  // Scroll detection for navbar transparency/blur effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      setIsScrolled(scrollPosition > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Check if we're on the contact page (no hero section)
  const isContactPage = pathname === '/contact'
  const isProjectsPage = pathname === '/projects'
  return (
    <header
      className={`
        sticky top-0 z-50 w-full transition-all duration-300 ease-in-out
        ${
          isContactPage || isScrolled
            ? 'backdrop-blur-md supports-[backdrop-filter]:bg-[#8A3E25]/95 border-b border-[#8A3E25]/40 shadow-lg'
            : 'bg-transparent border-b border-transparent'
        }
       ${
          isProjectsPage || isScrolled
            ? 'backdrop-blur-md supports-[backdrop-filter]:bg-[#8A3E25]/95 border-b border-[#8A3E25]/40 shadow-lg'
            : 'bg-transparent border-b border-transparent'
        }
      `}
      {...(theme ? { 'data-theme': theme } : {})}
    >
      <div className="npi-container">
        <div className="flex h-16 items-center">
          <Link
            href="/"
            className="flex items-center space-x-2 transition-transform duration-200 hover:scale-105 flex-shrink-0"
          >
            <Logo loading="eager" priority="high" />
          </Link>
          <HeaderNav data={data} />
        </div>
      </div>
    </header>
  )
}
