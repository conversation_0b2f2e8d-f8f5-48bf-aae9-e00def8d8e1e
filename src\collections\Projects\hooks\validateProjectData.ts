import type { CollectionBeforeValidateHook } from 'payload'

export const validateProjectData: CollectionBeforeValidateHook = ({ data, operation, req }) => {
  // Ensure data is properly structured for JSON serialization
  if (operation === 'create' || operation === 'update') {
    try {
      // Validate and sanitize rich text description
      if (data.description) {
        // Ensure description has proper Lexical structure
        if (typeof data.description === 'string') {
          // Convert string to proper Lexical format
          data.description = {
            root: {
              children: [
                {
                  children: [
                    {
                      detail: 0,
                      format: 0,
                      mode: 'normal',
                      style: '',
                      text: data.description,
                      type: 'text',
                      version: 1,
                    },
                  ],
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  type: 'paragraph',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'root',
              version: 1,
            },
          }
        } else if (typeof data.description === 'object' && !data.description.root) {
          // Wrap in proper root structure if missing
          data.description = {
            root: data.description,
          }
        }
      }

      // Validate timeline structure
      if (data.timeline) {
        // Ensure startDate is properly formatted
        if (data.timeline.startDate && typeof data.timeline.startDate === 'string') {
          try {
            // Validate date format
            const date = new Date(data.timeline.startDate)
            if (isNaN(date.getTime())) {
              req.payload.logger.error('Invalid startDate format:', data.timeline.startDate)
              throw new Error('Invalid start date format. Please use a valid date.')
            }
          } catch (error) {
            req.payload.logger.error('Date validation error:', error)
            throw new Error('Invalid start date format. Please use a valid date.')
          }
        }

        // Validate milestones array
        if (data.timeline.milestones && Array.isArray(data.timeline.milestones)) {
          data.timeline.milestones = data.timeline.milestones.map((milestone: any) => {
            if (milestone.targetDate && typeof milestone.targetDate === 'string') {
              try {
                const date = new Date(milestone.targetDate)
                if (isNaN(date.getTime())) {
                  milestone.targetDate = null
                }
              } catch {
                milestone.targetDate = null
              }
            }
            return milestone
          })
        }
      }

      // Validate budget structure
      if (data.budget) {
        // Ensure totalBudget is a number
        if (data.budget.totalBudget && typeof data.budget.totalBudget === 'string') {
          const numValue = parseFloat(data.budget.totalBudget)
          if (!isNaN(numValue)) {
            data.budget.totalBudget = numValue
          } else {
            data.budget.totalBudget = null
          }
        }

        // Validate funding sources
        if (data.budget.fundingSources && Array.isArray(data.budget.fundingSources)) {
          data.budget.fundingSources = data.budget.fundingSources.map((source: any) => {
            if (source.amount && typeof source.amount === 'string') {
              const numValue = parseFloat(source.amount)
              source.amount = !isNaN(numValue) ? numValue : null
            }
            if (source.percentage && typeof source.percentage === 'string') {
              const numValue = parseFloat(source.percentage)
              source.percentage = !isNaN(numValue) ? numValue : null
            }
            return source
          })
        }
      }

      // Validate impact structure
      if (data.impact) {
        // Convert string numbers to actual numbers
        const numberFields = ['beneficiaries', 'communities', 'jobsCreated']
        numberFields.forEach(field => {
          if (data.impact[field] && typeof data.impact[field] === 'string') {
            const numValue = parseInt(data.impact[field], 10)
            data.impact[field] = !isNaN(numValue) ? numValue : null
          }
        })
      }

      // Validate location coordinates
      if (data.location?.coordinates) {
        if (data.location.coordinates.latitude && typeof data.location.coordinates.latitude === 'string') {
          const numValue = parseFloat(data.location.coordinates.latitude)
          data.location.coordinates.latitude = !isNaN(numValue) ? numValue : null
        }
        if (data.location.coordinates.longitude && typeof data.location.coordinates.longitude === 'string') {
          const numValue = parseFloat(data.location.coordinates.longitude)
          data.location.coordinates.longitude = !isNaN(numValue) ? numValue : null
        }
      }

      // Validate array fields to prevent JSON serialization issues
      const arrayFields = ['gallery', 'tags']
      arrayFields.forEach(field => {
        if (data[field] && !Array.isArray(data[field])) {
          data[field] = []
        }
      })

      // Validate nested array fields
      if (data.timeline?.milestones && !Array.isArray(data.timeline.milestones)) {
        data.timeline.milestones = []
      }
      if (data.budget?.fundingSources && !Array.isArray(data.budget.fundingSources)) {
        data.budget.fundingSources = []
      }
      if (data.impact?.metrics && !Array.isArray(data.impact.metrics)) {
        data.impact.metrics = []
      }
      if (data.team?.implementingPartners && !Array.isArray(data.team.implementingPartners)) {
        data.team.implementingPartners = []
      }
      if (data.team?.keyPersonnel && !Array.isArray(data.team.keyPersonnel)) {
        data.team.keyPersonnel = []
      }
      if (data.resources?.documents && !Array.isArray(data.resources.documents)) {
        data.resources.documents = []
      }
      if (data.resources?.links && !Array.isArray(data.resources.links)) {
        data.resources.links = []
      }

      // Ensure boolean fields are properly set
      data.featured = Boolean(data.featured)
      data.published = data.published !== false // Default to true

      req.payload.logger.info('Project data validated successfully')
      
    } catch (error) {
      req.payload.logger.error('Project validation error:', error)
      throw new Error(`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  return data
}
