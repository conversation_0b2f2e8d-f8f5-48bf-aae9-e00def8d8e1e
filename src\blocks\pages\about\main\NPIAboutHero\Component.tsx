import React from 'react'
import Image from 'next/image'

interface NPIAboutHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPIAboutHeroBlock: React.FC<NPIAboutHeroProps> = ({
  title = 'About the Natural Products Industry Initiative',
  backgroundImage = '/assets/hero image.jpg',
}) => {
  return (
    <section className="relative min-h-[95vh] max-h-[95vh] overflow-hidden -mt-16 pt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Hero background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Solid overlay - no gradients */}
      <div className="absolute inset-0 bg-[#725242]/60" />

      {/* Top Center Title with blue accent */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-30 text-center max-w-5xl px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-white">
          {title}
        </h1>
      </div>

      {/* Bottom Left Text with reddish brown accents */}
      <div className="absolute bottom-8 left-8 z-30 max-w-sm">
        <div className="bg-[#EFE3BA] backdrop-blur-md p-6 border-l-4 border-[#8A3E25]">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed text-black">
            Kenya&apos;s rich biodiversity holds immense potential. We&apos;re dedicated to
            unlocking it <span className="text-[#8A3E25] font-semibold">sustainably</span>.
          </p>
        </div>
      </div>

      {/* Bottom Right Feature Card with mixed accents */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#FFFFFF] backdrop-blur-md p-6 shadow-lg max-w-xs border-2 border-[#25718A]">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-[#8A3E25]">Our Mission</h3>
          <p className="text-sm sm:text-base mb-4 text-black">
            Empowering local communities through natural product innovation and sustainable
            practices.
          </p>
          <button className="bg-[#8A3E25] text-white hover:bg-[#25718A] text-sm font-medium transition-colors px-4 py-2 border-2 border-[#8A3E25] hover:border-[#25718A]">
            Learn More &rarr;
          </button>
        </div>
      </div>
    </section>
  )
}
