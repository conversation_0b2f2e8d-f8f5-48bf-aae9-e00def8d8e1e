import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'

// Events page components
// Uses: NPIEventsHeroBlock, NPIEventsCalendarBlock from events

export const metadata: Metadata = {
  title: 'Events - Natural Products Industry Initiative',
  description:
    'Join us at conferences, workshops, and training sessions designed to advance natural products development and share knowledge across Kenya.',
}

const eventsPageLayout = [
  {
    blockType: 'npiEventsHero' as const,
  },
  {
    blockType: 'npiEventsCalendar' as const,
  },
]

export default function EventsPage() {
  return (
    <article className="min-h-screen">
      {eventsPageLayout.map((block, index) => (
        <section
          key={index}
          className={`
            ${index === 0 ? '' : '-mt-1'}
            relative
            ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
          `}
        >
          <RenderBlocks blocks={[block]} />
        </section>
      ))}
    </article>
  )
}
