import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'
import { NPIProjectsPageHeroComponent } from '@/heros/NPIProjectsHero'

export const metadata: Metadata = {
  title: 'Projects & Initiatives - Natural Products Industry Initiative',
  description:
    "Explore NPI's comprehensive projects and initiatives driving sustainable development through indigenous knowledge preservation, community empowerment, and natural products innovation across Kenya.",
}

const projectsPageLayout = [
  {
    blockType: 'npiProjectsListing' as const,
    id: 'projects-listing',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Projects Impact',
    variant: 'secondary',
  },
]

export default function ProjectsPage() {
  return (
    <article className="min-h-screen">
      <PageClient />

      {/* Hero Section */}
      <NPIProjectsPageHeroComponent />

      {/* Main Content Sections */}
      <main className="relative">
        {projectsPageLayout.map((block, index) => (
          <section
            key={index}
            className={`
              ${index === 0 ? '' : '-mt-1'}
              relative
              ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
            `}
          >
            <RenderBlocks blocks={[block]} />
          </section>
        ))}
      </main>
    </article>
  )
}
