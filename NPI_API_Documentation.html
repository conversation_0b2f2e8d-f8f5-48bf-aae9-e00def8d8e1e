
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>NPI Platform API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2D5016;
            border-bottom: 3px solid #2D5016;
            padding-bottom: 10px;
        }
        h2 {
            color: #3E2723;
            border-bottom: 2px solid #EFE3BA;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #725242;
            margin-top: 25px;
        }
        h4 {
            color: #8A3E25;
            margin-top: 20px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #EFE3BA;
            font-weight: bold;
        }
        .toc {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #25718A;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .endpoint {
            background-color: #f0f8ff;
            border-left: 4px solid #25718A;
            padding: 10px;
            margin: 10px 0;
        }
        .method-get {
            color: #28a745;
            font-weight: bold;
        }
        .method-post {
            color: #007bff;
            font-weight: bold;
        }
        .method-put {
            color: #ffc107;
            font-weight: bold;
        }
        .method-delete {
            color: #dc3545;
            font-weight: bold;
        }
        .status-code {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            color: white;
        }
        .status-200 { background-color: #28a745; }
        .status-400 { background-color: #ffc107; }
        .status-401 { background-color: #fd7e14; }
        .status-403 { background-color: #dc3545; }
        .status-404 { background-color: #6c757d; }
        .status-500 { background-color: #dc3545; }
        @media print {
            body { margin: 0; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <h1>NPI Platform API Documentation</h1><h2>Table of Contents</h2>
1. <a href="#overview">Overview</a>
2. <a href="#base-urls">Base URLs</a>
3. <a href="#authentication">Authentication</a>
4. <a href="#common-parameters">Common Parameters</a>
5. <a href="#response-format">Response Format</a>
6. <a href="#public-endpoints">Public Endpoints</a>
7. <a href="#authentication-endpoints">Authentication Endpoints</a>
8. <a href="#admin-endpoints">Admin Endpoints</a>
9. <a href="#graphql-api">GraphQL API</a>
10. <a href="#error-handling">Error Handling</a>
11. <a href="#rate-limiting">Rate Limiting</a>
12. <a href="#sdk-usage">SDK Usage</a>
13. <a href="#examples">Examples</a></p><h2>Overview</h2><p>The NPI (National Platform for Innovation) API provides comprehensive access to projects, success stories, resources, news, partnerships, investment opportunities, and more. The API is built on PayloadCMS and supports both REST and GraphQL interfaces.</p><h2>Base URLs</h2><pre><code>Production: https://npi-website.vercel.app/api
Development: http://localhost:3000/api
GraphQL: /api/graphql
Admin Panel: /admin
API Testing: /test-api
</code></pre><h2>Authentication</h2><h3>Public Access</h3>
Most read endpoints are publicly accessible without authentication.</p><h3>Protected Access</h3>
Admin endpoints require JWT authentication:
<pre><code>Authorization: Bearer <jwt_token>
</code></pre><h3>Authentication Flow</h3>
1. Login via <code>/api/users/login</code>
2. Receive JWT token
3. Include token in subsequent requests
4. Refresh token via <code>/api/users/refresh-token</code></p><h2>Common Parameters</h2><p>All list endpoints support these query parameters:</p><table><tr><th>Parameter</th><th>Type</th><th>Default</th><th>Description</th></tr><tr><td><code>page</code></td><td>integer</td><td>1</td><td>Page number</td></tr><tr><td><code>limit</code></td><td>integer</td><td>20</td><td>Items per page (max 100)</td></tr><tr><td><code>sort</code></td><td>string</td><td><code>-updatedAt</code></td><td>Sort field and direction</td></tr><tr><td><code>search</code></td><td>string</td><td>-</td><td>Text search</td></tr><tr><td><code>featured</code></td><td>boolean</td><td>-</td><td>Filter featured items</td></tr></table>
<h2>Response Format</h2><h3>Success Response</h3>
<pre><code>{
  "data": [...],
  "totalDocs": 150,
  "page": 1,
  "limit": 20,
  "totalPages": 8,
  "hasNextPage": true,
  "hasPrevPage": false
}
</code></pre><h3>Error Response</h3>
<pre><code>{
  "error": "Error Type",
  "message": "Detailed error message",
  "code": "ERROR_CODE",
  "statusCode": 400,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
</code></pre><h2>Public Endpoints</h2><h3>Projects API</h3><h4>List Projects</h4>
<pre><code><span class="method-get">GET</span> /api/projects
</code></pre><p><strong>Query Parameters:</strong>
- <code>category</code>: Filter by category
- <code>pillar</code>: Filter by strategic pillar
- <code>status</code>: Filter by status
- <code>county</code>: Filter by county ID
- <code>featured</code>: Filter featured projects
- <code>search</code>: Text search</p><p><strong>Example:</strong>
<pre><code><span class="method-get">GET</span> /api/projects?category=community-empowerment&featured=true&limit=10
</code></pre><h4>Get Single Project</h4>
<pre><code><span class="method-get">GET</span> /api/projects/:id
</code></pre><h3>Success Stories API</h3><h4>List Success Stories</h4>
<pre><code><span class="method-get">GET</span> /api/success-stories
</code></pre><p><strong>Query Parameters:</strong>
- <code>category</code>: Filter by category
- <code>county</code>: Filter by county ID</p><h4>Get Success Story</h4>
<pre><code><span class="method-get">GET</span> /api/success-stories/:id
</code></pre><h3>Resources API</h3><h4>List Resources</h4>
<pre><code><span class="method-get">GET</span> /api/resources
</code></pre><p><strong>Query Parameters:</strong>
- <code>type</code>: Filter by resource type
- <code>category</code>: Filter by category
- <code>language</code>: Filter by language
- <code>access</code>: Filter by access level</p><h4>Get Resource</h4>
<pre><code><span class="method-get">GET</span> /api/resources/:id
</code></pre><h4>Download Resource</h4>
<pre><code><span class="method-get">GET</span> /api/resources/:id/download
</code></pre><h3>News API</h3><h4>List News Articles</h4>
<pre><code><span class="method-get">GET</span> /api/news
</code></pre><p><strong>Query Parameters:</strong>
- <code>category</code>: Filter by category
- <code>urgent</code>: Filter urgent news
- <code>author</code>: Filter by author name</p><h4>Get News Article</h4>
<pre><code><span class="method-get">GET</span> /api/news/:id
</code></pre><h3>Media Gallery API</h3><h4>List Media Items</h4>
<pre><code><span class="method-get">GET</span> /api/media-gallery
</code></pre><p><strong>Query Parameters:</strong>
- <code>type</code>: Filter by media type
- <code>category</code>: Filter by category
- <code>event</code>: Filter by event ID
- <code>project</code>: Filter by project ID
- <code>license</code>: Filter by license type</p><h4>Get Media Item</h4>
<pre><code><span class="method-get">GET</span> /api/media-gallery/:id
</code></pre><h3>Partnerships API</h3><h4>List Partnerships</h4>
<pre><code><span class="method-get">GET</span> /api/partnerships
</code></pre><p><strong>Query Parameters:</strong>
- <code>type</code>: Filter by partnership type
- <code>status</code>: Filter by status
- <code>partner</code>: Filter by partner ID</p><h4>Get Partnership</h4>
<pre><code><span class="method-get">GET</span> /api/partnerships/:id
</code></pre><h3>Investment Opportunities API</h3><h4>List Investment Opportunities</h4>
<pre><code><span class="method-get">GET</span> /api/investment-opportunities
</code></pre><p><strong>Query Parameters:</strong>
- <code>sector</code>: Filter by sector
- <code>investmentType</code>: Filter by investment type
- <code>status</code>: Filter by status
- <code>urgent</code>: Filter urgent opportunities
- <code>minAmount</code>: Minimum funding amount
- <code>maxAmount</code>: Maximum funding amount</p><h4>Get Investment Opportunity</h4>
<pre><code><span class="method-get">GET</span> /api/investment-opportunities/:id
</code></pre><h3>Counties API</h3><h4>List Counties</h4>
<pre><code><span class="method-get">GET</span> /api/counties
</code></pre><h4>Get County</h4>
<pre><code><span class="method-get">GET</span> /api/counties/:id
</code></pre><h4>Get Counties in Bounds</h4>
<pre><code><span class="method-get">GET</span> /api/counties/bounds?north=1.0&south=-5.0&east=42.0&west=33.0
</code></pre><h3>Events API</h3><h4>List Events</h4>
<pre><code><span class="method-get">GET</span> /api/events
</code></pre><p><strong>Query Parameters:</strong>
- <code>type</code>: Filter by event type
- <code>status</code>: Filter by status
- <code>county</code>: Filter by county
- <code>upcoming</code>: Filter upcoming events
- <code>featured</code>: Filter featured events</p><h4>Get Event</h4>
<pre><code><span class="method-get">GET</span> /api/events/:id
</code></pre><h3>Contact API</h3><h4>Submit Contact Form</h4>
<pre><code><span class="method-post">POST</span> /api/contact-submissions
Content-Type: application/json</p><p>{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+254700000000",
  "organization": "Example Org",
  "role": "Manager",
  "subject": "Partnership Inquiry",
  "category": "partnership",
  "priority": "medium",
  "message": "I am interested in partnering with NPI...",
  "location": {
    "county": "county-id",
    "city": "Nairobi",
    "country": "Kenya"
  }
}
</code></pre><p><strong>Response:</strong>
<pre><code>{
  "success": true,
  "message": "Contact submission received successfully",
  "submissionId": "submission-id",
  "status": "new"
}
</code></pre><h2>Authentication Endpoints</h2><h3>Login</h3>
<pre><code><span class="method-post">POST</span> /api/users/login
Content-Type: application/json</p><p>{
  "email": "<EMAIL>",
  "password": "password123"
}
</code></pre><p><strong>Response:</strong>
<pre><code>{
  "message": "Auth Passed",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "exp": **********
}
</code></pre><h3>Logout</h3>
<pre><code><span class="method-post">POST</span> /api/users/logout
Authorization: Bearer <token>
</code></pre><h3>Get Current User</h3>
<pre><code><span class="method-get">GET</span> /api/users/me
Authorization: Bearer <token>
</code></pre><h3>Refresh Token</h3>
<pre><code><span class="method-post">POST</span> /api/users/refresh-token
Authorization: Bearer <token>
</code></pre><h3>Forgot Password</h3>
<pre><code><span class="method-post">POST</span> /api/users/forgot-password
Content-Type: application/json</p><p>{
  "email": "<EMAIL>"
}
</code></pre><h3>Reset Password</h3>
<pre><code><span class="method-post">POST</span> /api/users/reset-password
Content-Type: application/json</p><p>{
  "token": "reset-token-from-email",
  "password": "newpassword123"
}
</code></pre><h2>Admin Endpoints</h2><p>All admin endpoints require authentication and appropriate permissions.</p><h3>Projects Management</h3><h4>Create Project</h4>
<pre><code><span class="method-post">POST</span> /api/projects
Authorization: Bearer <token>
Content-Type: application/json</p><p>{
  "title": "New Community Project",
  "summary": "Empowering communities through sustainable development",
  "category": "community-empowerment",
  "pillar": "community-innovation",
  "status": "active",
  "timeline": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T00:00:00.000Z"
  },
  "budget": {
    "totalBudget": 500000,
    "currency": "KES"
  },
  "location": {
    "counties": ["nairobi", "kiambu"],
    "specificLocation": "Nairobi and surrounding areas"
  },
  "featured": false,
  "published": true
}
</code></pre><h4>Update Project</h4>
<pre><code><span class="method-put">PUT</span> /api/projects/:id
Authorization: Bearer <token>
Content-Type: application/json</p><p>{
  "title": "Updated Project Title",
  "status": "completed",
  "impact": {
    "beneficiaries": 1200,
    "communities": 6,
    "jobsCreated": 75
  }
}
</code></pre><h4>Delete Project</h4>
<pre><code><span class="method-delete">DELETE</span> /api/projects/:id
Authorization: Bearer <token>
</code></pre><h3>Contact Submissions Management</h3><h4>List Contact Submissions</h4>
<pre><code><span class="method-get">GET</span> /api/contact-submissions
Authorization: Bearer <token>
</code></pre><h4>Get Contact Submission</h4>
<pre><code><span class="method-get">GET</span> /api/contact-submissions/:id
Authorization: Bearer <token>
</code></pre><h4>Update Contact Submission</h4>
<pre><code><span class="method-put">PUT</span> /api/contact-submissions/:id
Authorization: Bearer <token>
Content-Type: application/json</p><p>{
  "status": "in-progress",
  "assignedTo": "user-id",
  "department": "partnerships",
  "priority": "high"
}
</code></pre><h3>Counties Management</h3><h4>Create County</h4>
<pre><code><span class="method-post">POST</span> /api/counties
Authorization: Bearer <token>
Content-Type: application/json</p><p>{
  "name": "New County",
  "code": "001"
}
</code></pre><h4>Update County</h4>
<pre><code><span class="method-put">PUT</span> /api/counties/:id
Authorization: Bearer <token>
Content-Type: application/json</p><p>{
  "name": "Updated County Name"
}
</code></pre><h4>Delete County</h4>
<pre><code><span class="method-delete">DELETE</span> /api/counties/:id
Authorization: Bearer <token>
</code></pre><h2>GraphQL API</h2><h3>Endpoint</h3>
<pre><code><span class="method-post">POST</span> /api/graphql
Content-Type: application/json
</code></pre><h3>Example Query</h3>
<pre><code>query GetProjects($limit: Int, $where: Project_where) {
  Projects(limit: $limit, where: $where) {
    docs {
      id
      title
      summary
      category
      status
      featured
      createdAt
      updatedAt
    }
    totalDocs
    hasNextPage
    hasPrevPage
  }
}
</code></pre><h3>Example Variables</h3>
<pre><code>{
  "limit": 10,
  "where": {
    "featured": {
      "equals": true
    }
  }
}
</code></pre><h2>Error Handling</h2><h3>Error Codes</h3><table><tr><th>Code</th><th>Status</th><th>Description</th></tr><tr><td><code>VALIDATION_ERROR</code></td><td>400</td><td>Input validation failed</td></tr><tr><td><code>AUTHENTICATION_ERROR</code></td><td>401</td><td>Authentication required</td></tr><tr><td><code>AUTHORIZATION_ERROR</code></td><td>403</td><td>Insufficient permissions</td></tr><tr><td><code>NOT_FOUND_ERROR</code></td><td>404</td><td>Resource not found</td></tr><tr><td><code>CONFLICT_ERROR</code></td><td>409</td><td>Resource conflict</td></tr><tr><td><code>RATE_LIMIT_ERROR</code></td><td>429</td><td>Rate limit exceeded</td></tr><tr><td><code>INTERNAL_SERVER_ERROR</code></td><td>500</td><td>Server error</td></tr><tr><td><code>SERVICE_UNAVAILABLE_ERROR</code></td><td>503</td><td>Service unavailable</td></tr></table>
<h3>Error Response Example</h3>
<pre><code>{
  "error": "VALIDATION_ERROR",
  "message": "Name, email, subject, message, and category are required",
  "statusCode": 400,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestId": "req_123456"
}
</code></pre><h2>Rate Limiting</h2><p>API endpoints are rate limited to ensure fair usage:</p><p>- <strong>Authenticated users</strong>: 1000 requests per 15 minutes
- <strong>Anonymous users</strong>: 100 requests per 15 minutes
- <strong>Contact form</strong>: 5 submissions per hour per IP
- <strong>Authentication endpoints</strong>: 10 attempts per 15 minutes per IP</p><h2>SDK Usage</h2><h3>JavaScript/TypeScript SDK</h3><pre><code>import { cmsAPI } from '@/lib/cms'</p><p>// Fetch projects
const projects = await cmsAPI.projects.getAll({
  category: 'community-empowerment',
  featured: true,
  limit: 10
})</p><p>// Submit contact form
await cmsAPI.contact.submit({
  name: 'John Doe',
  email: '<EMAIL>',
  subject: 'Partnership Inquiry',
  category: 'partnership',
  message: 'I am interested in partnering with NPI...'
})</p><p>// Get single project
const project = await cmsAPI.projects.getById('project-id')</p><p>// Search resources
const resources = await cmsAPI.resources.getAll({
  search: 'traditional medicine',
  type: 'research-report'
})
</code></pre><h3>React Hooks</h3><pre><code>import { useProjects, useContactForm } from '@/lib/cms'</p><p>function ProjectsList() {
  const { data: projects, loading, error } = useProjects({
    featured: true,
    limit: 6
  })</p><p>  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div></p><p>  return (
    <div>
      {projects.map(project => (
        <div key={project.id}>{project.title}</div>
      ))}
    </div>
  )
}</p><p>function ContactForm() {
  const { submitForm, loading, error, success } = useContactForm()</p><p>  const handleSubmit = async (formData) => {
    await submitForm(formData)
  }</p><p>  return (
    <form onSubmit={handleSubmit}>
      {/<em> Form fields </em>/}
      {error && <div>Error: {error}</div>}
      {success && <div>Form submitted successfully!</div>}
    </form>
  )
}
</code></pre><h2>Examples</h2><h3>Fetch Featured Projects</h3>
<pre><code>const response = await fetch('/api/projects?featured=true&limit=6')
const { data: projects } = await response.json()</p><p>projects.forEach(project => {
  console.log(<code>${project.title} - ${project.category}</code>)
})
</code></pre><h3>Submit Contact Form with Error Handling</h3>
<pre><code>try {
  const response = await fetch('/api/contact-submissions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      name: 'John Doe',
      email: '<EMAIL>',
      subject: 'Partnership Inquiry',
      category: 'partnership',
      message: 'I am interested in partnering with NPI...'
    })
  })</p><p>  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message)
  }</p><p>  const result = await response.json()
  console.log('Submission successful:', result.submissionId)
} catch (error) {
  console.error('Submission failed:', error.message)
}
</code></pre><h3>Search Resources with Filters</h3>
<pre><code>const searchParams = new URLSearchParams({
  search: 'traditional medicine',
  type: 'research-report',
  language: 'en',
  limit: '20'
})</p><p>const response = await fetch(<code>/api/resources?${searchParams}</code>)
const { data: resources } = await response.json()
</code></pre><h3>Authentication Flow Example</h3>
<pre><code>// Login
const loginResponse = await fetch('/api/users/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
})</p><p>const { token, user } = await loginResponse.json()
localStorage.setItem('auth-token', token)</p><p>// Make authenticated request
const projectsResponse = await fetch('/api/projects', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': <code>Bearer ${token}</code>
  },
  body: JSON.stringify({
    title: 'New Project',
    category: 'community-empowerment',
    status: 'active'
  })
})
</code></pre><h3>Pagination Example</h3>
<pre><code>async function fetchAllProjects() {
  let allProjects = []
  let page = 1
  let hasMore = true</p><p>  while (hasMore) {
    const response = await fetch(<code>/api/projects?page=${page}&limit=50</code>)
    const data = await response.json()</p><p>    allProjects = [...allProjects, ...data.data]
    hasMore = data.hasNextPage
    page++
  }</p><p>  return allProjects
}
</code></pre><h2>Data Types</h2><h3>Media Object</h3>
<pre><code>{
  "id": "media-id",
  "filename": "image.jpg",
  "url": "https://storage.url/image.jpg",
  "alt": "Alt text",
  "width": 1920,
  "height": 1080,
  "mimeType": "image/jpeg",
  "filesize": 1024000
}
</code></pre><h3>County Object</h3>
<pre><code>{
  "id": "county-id",
  "name": "Nairobi",
  "code": "047"
}
</code></pre><h3>Rich Text Object</h3>
<pre><code>{
  "root": {
    "children": [
      {
        "children": [
          {
            "detail": 0,
            "format": 0,
            "mode": "normal",
            "style": "",
            "text": "This is a paragraph of text.",
            "type": "text",
            "version": 1
          }
        ],
        "direction": "ltr",
        "format": "",
        "indent": 0,
        "type": "paragraph",
        "version": 1
      }
    ],
    "direction": "ltr",
    "format": "",
    "indent": 0,
    "type": "root",
    "version": 1
  }
}
</code></pre><h3>Project Object (Full)</h3>
<pre><code>{
  "id": "project-id",
  "title": "Community Empowerment Initiative",
  "summary": "Empowering local communities through skill development",
  "category": "community-empowerment",
  "pillar": "community-innovation",
  "status": "active",
  "timeline": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T00:00:00.000Z",
    "milestones": [
      {
        "title": "Project Launch",
        "date": "2024-01-01T00:00:00.000Z",
        "completed": true
      }
    ]
  },
  "budget": {
    "totalBudget": 500000,
    "currency": "KES",
    "fundingSources": [
      {
        "source": "Government Grant",
        "amount": 300000,
        "percentage": 60
      }
    ]
  },
  "location": {
    "counties": ["nairobi", "kiambu"],
    "specificLocation": "Nairobi and surrounding areas",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    }
  },
  "impact": {
    "beneficiaries": 1000,
    "communities": 5,
    "jobsCreated": 50,
    "metrics": [
      {
        "metric": "Training Sessions Conducted",
        "value": "25",
        "unit": "sessions"
      }
    ]
  },
  "team": {
    "projectManager": {
      "name": "John Doe",
      "role": "Project Manager",
      "contact": "<EMAIL>"
    }
  },
  "tags": [
    { "tag": "community" },
    { "tag": "empowerment" }
  ],
  "featured": true,
  "published": true,
  "slug": "community-empowerment-initiative",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
</code></pre><h2>Testing</h2><h3>API Testing Dashboard</h3>
Visit <code>/test-api</code> in your browser for an interactive API testing interface:
- <strong>URL</strong>: <code>http://localhost:3000/test-api</code>
- <strong>Features</strong>: Test all endpoints, authentication, real-time results
- <strong>Authentication</strong>: Login directly from the testing interface</p><h3>Manual Testing with Browser Console</h3>
<pre><code>// Test public endpoints
fetch('/api/projects?featured=true')
  .then(r => r.json())
  .then(console.log)</p><p>// Test with authentication
fetch('/api/contact-submissions', {
  headers: { 'Authorization': 'Bearer ' + localStorage.getItem('auth-token') }
})
.then(r => r.json())
.then(console.log)
</code></pre><h2>Database Configuration</h2><p>The API uses PostgreSQL as the primary database:</p><pre><code><h1>PostgreSQL Configuration</h1>
DATABASE_URI=postgresql://username:password@localhost:5432/npi_cms</p><h1>For cloud databases (e.g., Supabase, AWS RDS)</h1>
DATABASE_URI=********************************/dbname?sslmode=require
</code></pre><h2>Security Features</h2><p>- <strong>Password Hashing</strong>: Automatic bcrypt hashing for user passwords
- <strong>JWT Tokens</strong>: Secure, stateless authentication with configurable expiration
- <strong>Account Locking</strong>: Automatic account locking after failed login attempts
- <strong>Rate Limiting</strong>: Built-in protection against brute force attacks
- <strong>Input Validation</strong>: Comprehensive validation for all API inputs
- <strong>CORS Protection</strong>: Configurable CORS policies
- <strong>SQL Injection Protection</strong>: Built-in protection via ORM</p><h2>Support</h2><p>For API support and questions:
- <strong>Documentation</strong>: See <code>/docs</code> directory for additional documentation
- <strong>Testing</strong>: Use <code>/test-api</code> for interactive testing
- <strong>Issues</strong>: Report issues through the platform's issue tracking system</p><p>---</p><p><em>This documentation covers the NPI Platform API v1.0. For the latest updates and changes, please refer to the platform's changelog and release notes.</em>
```
</p>
</body>
</html>
