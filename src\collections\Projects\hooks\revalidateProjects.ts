import type { CollectionAfterChangeHook } from 'payload'
import { revalidatePath, revalidateTag } from 'next/cache'

export const revalidateProjects: CollectionAfterChangeHook = ({
  doc,
  previousDoc,
  req: { payload, context },
}) => {
  if (!context.disableRevalidate) {
    try {
      // Revalidate projects listing page
      payload.logger.info('Revalidating projects pages')
      
      revalidatePath('/projects')
      revalidateTag('projects')
      
      // Revalidate homepage if project is featured
      if (doc.featured || previousDoc?.featured) {
        payload.logger.info('Revalidating homepage for featured project')
        revalidatePath('/')
        revalidateTag('homepage')
      }

      // Revalidate individual project page if it has a slug
      if (doc.slug) {
        const projectPath = `/projects/${doc.slug}`
        payload.logger.info(`Revalidating project page: ${projectPath}`)
        revalidatePath(projectPath)
      }

      // If the project was previously published but now unpublished, revalidate the old path
      if (previousDoc?.published && !doc.published && previousDoc.slug) {
        const oldPath = `/projects/${previousDoc.slug}`
        payload.logger.info(`Revalidating old project path: ${oldPath}`)
        revalidatePath(oldPath)
      }

    } catch (error) {
      payload.logger.error('Error during project revalidation:', error)
    }
  }
  
  return doc
}
