'use client'

import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardDescription,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'

interface Project {
  title: string
  description: string
  image: string
  category: string
  location?: string
  status: 'active' | 'completed' | 'upcoming'
  link: string
}

interface NPIFeaturedProjectsProps {
  title?: string
  description?: string
  projects?: Project[]
}

export const NPIFeaturedProjectsBlock: React.FC<NPIFeaturedProjectsProps> = ({
  title = 'Featured Projects',
  description = "Discover our key initiatives transforming Kenya's natural products landscape through community-driven innovation and sustainable development.",
  projects = [
    {
      title: 'Indigenous Knowledge Documentation Project',
      description:
        'Comprehensive documentation of traditional knowledge systems across 47 counties, preserving invaluable cultural heritage for future generations.',
      image: '/assets/product 1.jpg',
      category: 'Knowledge Preservation',
      location: 'Nationwide',
      status: 'active',
      link: '/projects/knowledge-documentation',
    },
    {
      title: 'Community-Based Product Development',
      description:
        'Empowering local communities to develop market-ready products from indigenous plants and traditional practices.',
      image: '/assets/product 2.jpg',
      category: 'Product Development',
      location: 'Multiple Counties',
      status: 'active',
      link: '/projects/community-development',
    },
    {
      title: 'Natural Products Innovation Hub',
      description:
        'State-of-the-art research facility supporting product development, testing, and commercialization of natural products.',
      image: '/assets/product 3.jpg',
      category: 'Research & Innovation',
      location: 'Nairobi',
      status: 'upcoming',
      link: '/projects/innovation-hub',
    },
    {
      title: 'Women & Youth Empowerment Initiative',
      description:
        'Targeted programs supporting women and youth entrepreneurs in the natural products value chain.',
      image: '/assets/product 4.jpg',
      category: 'Capacity Building',
      location: 'Rural Communities',
      status: 'active',
      link: '/projects/youth-empowerment',
    },
    {
      title: 'Market Access & Export Development',
      description:
        'Creating pathways for Kenyan natural products to reach local and international markets.',
      image:
        'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop&crop=center',
      category: 'Market Development',
      location: 'Kenya & International',
      status: 'active',
      link: '/projects/market-access',
    },
    {
      title: 'Intellectual Property Protection',
      description:
        'Safeguarding traditional knowledge and supporting communities in protecting their intellectual property rights.',
      image:
        'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=600&fit=crop&crop=center',
      category: 'Legal & IP',
      location: 'Nationwide',
      status: 'active',
      link: '/projects/ip-protection',
    },
  ],
}) => {
  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return 'bg-[#25718A] text-white shadow-lg'
      case 'completed':
        return 'bg-[#725242] text-white shadow-lg'
      case 'upcoming':
        return 'bg-[#8A3E25] text-white shadow-lg'
      default:
        return 'bg-black text-white shadow-lg'
    }
  }

  return (
    <NPISection size="sm" className="bg-white relative overflow-hidden">
      {/* Simple Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Subtle pattern */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `
            linear-gradient(90deg, #725242 1px, transparent 1px),
            linear-gradient(#725242 1px, transparent 1px)
          `,
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-4 py-2 bg-[#8A3E25]/15 border border-[#8A3E25]/30 text-[#8A3E25] text-sm font-medium mb-3"
          >
            <span className="w-2 h-2 bg-[#8A3E25] mr-3"></span>
            Featured Projects
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-2 text-black font-bold text-2xl lg:text-3xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <NPISectionDescription className="font-light leading-[1.6] text-black max-w-xl mx-auto text-sm lg:text-base">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {projects.slice(0, 4).map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.7,
                delay: index * 0.15,
                type: 'spring',
                stiffness: 100,
              }}
              whileHover={{ y: -6 }}
            >
              <NPICard
                className={`overflow-hidden shadow-lg border-2 hover:shadow-xl group transition-all duration-300 flex flex-col aspect-square w-full hover:scale-[1.05] hover:-translate-y-2 ${
                  index % 4 === 0
                    ? 'bg-[#725242] border-[#725242] hover:border-[#8A3E25] hover:shadow-[#725242]/30'
                    : index % 4 === 1
                      ? 'bg-[#EFE3BA] border-[#EFE3BA] hover:border-[#725242] hover:shadow-[#EFE3BA]/30'
                      : index % 4 === 2
                        ? 'bg-[#8A3E25] border-[#8A3E25] hover:border-[#725242] hover:shadow-[#8A3E25]/30'
                        : 'bg-white border-white hover:border-[#8A3E25] hover:shadow-white/30'
                }`}
              >
                {/* Square Image Section - Takes up top half */}
                <div className="relative h-1/2 w-full flex-shrink-0 overflow-hidden">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  />
                  <div
                    className={`absolute inset-0 ${
                      index % 4 === 0
                        ? 'bg-black/40'
                        : index % 4 === 1
                          ? 'bg-[#725242]/40'
                          : index % 4 === 2
                            ? 'bg-black/40'
                            : 'bg-[#8A3E25]/40'
                    }`}
                  />
                  <div className="absolute top-2 left-2">
                    <span
                      className={`px-2 py-1 text-xs font-bold shadow-lg ${getStatusColor(project.status)}`}
                    >
                      {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                    </span>
                  </div>
                  {/* Decorative corner accent */}
                  <div className="absolute top-0 right-0 w-6 h-6 bg-[#25718A]/50"></div>
                </div>

                {/* Content Section - Takes up bottom half */}
                <NPICardHeader
                  className={`h-1/2 p-3 flex-shrink-0 flex flex-col justify-between ${
                    index % 4 === 0
                      ? 'bg-white'
                      : index % 4 === 1
                        ? 'bg-white'
                        : index % 4 === 2
                          ? 'bg-white'
                          : 'bg-white'
                  }`}
                >
                  <div
                    className={`text-xs font-bold uppercase tracking-wide mb-1 ${
                      index % 4 === 0
                        ? 'text-[#725242]'
                        : index % 4 === 1
                          ? 'text-[#725242]'
                          : index % 4 === 2
                            ? 'text-[#725242]'
                            : 'text-[#725242]'
                    }`}
                  >
                    {project.category}
                  </div>
                  <NPICardTitle
                    className={`text-sm font-bold leading-tight transition-colors line-clamp-2 flex-1 flex items-start ${
                      index % 4 === 0
                        ? 'text-black group-hover:text-[#8A3E25]'
                        : index % 4 === 1
                          ? 'text-black group-hover:text-[#8A3E25]'
                          : index % 4 === 2
                            ? 'text-black group-hover:text-[#8A3E25]'
                            : 'text-black group-hover:text-[#8A3E25]'
                    }`}
                  >
                    {project.title}
                  </NPICardTitle>
                  <div className="flex items-center justify-between mt-2">
                    {project.location && (
                      <div
                        className={`text-xs font-medium flex items-center gap-1 ${
                          index % 4 === 0
                            ? 'text-[#725242]'
                            : index % 4 === 1
                              ? 'text-[#725242]'
                              : index % 4 === 2
                                ? 'text-[#725242]'
                                : 'text-[#725242]'
                        }`}
                      >
                        <span
                          className={`w-1 h-1 ${
                            index % 4 === 0
                              ? 'bg-[#25718A]'
                              : index % 4 === 1
                                ? 'bg-[#8A3E25]'
                                : index % 4 === 2
                                  ? 'bg-[#25718A]'
                                  : 'bg-[#725242]'
                          }`}
                        ></span>
                        <span className="line-clamp-1">{project.location}</span>
                      </div>
                    )}
                  </div>
                </NPICardHeader>

                {/* Fixed Content Section - Compact height */}
                <NPICardContent className="h-[120px] px-3 py-2 flex-shrink-0 flex flex-col justify-start">
                  <NPICardDescription className="text-xs leading-[1.4] text-muted-foreground line-clamp-5 h-full overflow-hidden">
                    {project.description}
                  </NPICardDescription>
                </NPICardContent>

                {/* Fixed Footer Section - Compact height */}
                <NPICardFooter className="h-[50px] px-3 pb-3 pt-0 flex-shrink-0 flex items-center">
                  <NPIButton
                    asChild
                    className="w-full h-[32px] bg-[#8A3E25] text-white hover:bg-[#25718A] font-bold transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 flex items-center justify-center border-2 border-[#8A3E25] hover:border-[#25718A]"
                  >
                    <Link
                      href={project.link}
                      className="flex items-center justify-center w-full h-full"
                    >
                      Learn More
                    </Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-6"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <NPIButton
            asChild
            className="bg-[#8A3E25] hover:bg-[#25718A] text-white font-bold px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 border-2 border-[#8A3E25] hover:border-[#25718A]"
          >
            <Link href="/projects" className="flex items-center gap-2">
              View All Projects
              <motion.span
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
              >
                →
              </motion.span>
            </Link>
          </NPIButton>
        </motion.div>
      </div>
    </NPISection>
  )
}
