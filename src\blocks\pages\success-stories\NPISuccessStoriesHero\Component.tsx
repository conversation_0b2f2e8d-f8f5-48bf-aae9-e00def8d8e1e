import React from 'react'
import Image from 'next/image'

interface NPISuccessStoriesHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPISuccessStoriesHeroBlock: React.FC<NPISuccessStoriesHeroProps> = ({
  title = 'Success Stories',
  backgroundImage = '/assets/product 1.jpg',
}) => {
  return (
    <section className="relative min-h-[95vh] max-h-[95vh] overflow-hidden -mt-16 pt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full z-0 bg-[#8A3E25]">
        <Image
          src={backgroundImage}
          alt="Hero background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Solid overlay - no gradients */}
      <div className="absolute inset-0 bg-black/50 z-10" />

      {/* Top Left Title */}
      <div className="absolute top-24 left-8 z-30">
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-4">{title}</h1>
        <div className="w-24 h-1 bg-[#8A3E25]" />
      </div>

      {/* Bottom Right Feature Card with balanced 6-color palette */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#EFE3BA] p-6 shadow-lg max-w-xs border-2 border-[#8A3E25]">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-black">Inspiring Stories</h3>
          <p className="text-sm sm:text-base mb-4 text-[#725242]">
            Discover real impact stories of transformation, empowerment, and sustainable development
            across Kenya&apos;s communities.
          </p>
          <a
            href="#success-stories"
            className="text-[#25718A] hover:text-[#8A3E25] text-sm font-medium transition-colors"
          >
            Explore Success Stories &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
