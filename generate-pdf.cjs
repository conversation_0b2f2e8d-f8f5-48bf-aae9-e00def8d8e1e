const fs = require('fs');
const path = require('path');

// Simple HTML template for PDF generation
const htmlTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>NPI Platform API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2D5016;
            border-bottom: 3px solid #2D5016;
            padding-bottom: 10px;
        }
        h2 {
            color: #3E2723;
            border-bottom: 2px solid #EFE3BA;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #725242;
            margin-top: 25px;
        }
        h4 {
            color: #8A3E25;
            margin-top: 20px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #EFE3BA;
            font-weight: bold;
        }
        .toc {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #25718A;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .endpoint {
            background-color: #f0f8ff;
            border-left: 4px solid #25718A;
            padding: 10px;
            margin: 10px 0;
        }
        .method-get {
            color: #28a745;
            font-weight: bold;
        }
        .method-post {
            color: #007bff;
            font-weight: bold;
        }
        .method-put {
            color: #ffc107;
            font-weight: bold;
        }
        .method-delete {
            color: #dc3545;
            font-weight: bold;
        }
        .status-code {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            color: white;
        }
        .status-200 { background-color: #28a745; }
        .status-400 { background-color: #ffc107; }
        .status-401 { background-color: #fd7e14; }
        .status-403 { background-color: #dc3545; }
        .status-404 { background-color: #6c757d; }
        .status-500 { background-color: #dc3545; }
        @media print {
            body { margin: 0; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    {{CONTENT}}
</body>
</html>
`;

// Function to convert markdown to basic HTML
function markdownToHtml(markdown) {
    let html = markdown;
    
    // Headers
    html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
    
    // Code blocks
    html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>');
    
    // Inline code
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
    
    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Italic
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
    
    // Tables
    html = html.replace(/\|(.+)\|\n\|[-\s|]+\|\n((?:\|.+\|\n?)*)/g, function(match, header, rows) {
        const headerCells = header.split('|').map(cell => cell.trim()).filter(cell => cell);
        const headerRow = '<tr>' + headerCells.map(cell => `<th>${cell}</th>`).join('') + '</tr>';
        
        const bodyRows = rows.trim().split('\n').map(row => {
            const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell);
            return '<tr>' + cells.map(cell => `<td>${cell}</td>`).join('') + '</tr>';
        }).join('');
        
        return `<table>${headerRow}${bodyRows}</table>`;
    });
    
    // Line breaks
    html = html.replace(/\n\n/g, '</p><p>');
    html = '<p>' + html + '</p>';
    
    // Clean up empty paragraphs
    html = html.replace(/<p><\/p>/g, '');
    html = html.replace(/<p>(<h[1-6]>)/g, '$1');
    html = html.replace(/(<\/h[1-6]>)<\/p>/g, '$1');
    html = html.replace(/<p>(<table>)/g, '$1');
    html = html.replace(/(<\/table>)<\/p>/g, '$1');
    html = html.replace(/<p>(<pre>)/g, '$1');
    html = html.replace(/(<\/pre>)<\/p>/g, '$1');
    
    // Add HTTP method styling
    html = html.replace(/GET /g, '<span class="method-get">GET</span> ');
    html = html.replace(/POST /g, '<span class="method-post">POST</span> ');
    html = html.replace(/PUT /g, '<span class="method-put">PUT</span> ');
    html = html.replace(/DELETE /g, '<span class="method-delete">DELETE</span> ');
    
    return html;
}

// Read the markdown file
const markdownPath = path.join(__dirname, 'NPI_API_Documentation.md');
const markdownContent = fs.readFileSync(markdownPath, 'utf8');

// Convert to HTML
const htmlContent = markdownToHtml(markdownContent);
const finalHtml = htmlTemplate.replace('{{CONTENT}}', htmlContent);

// Write HTML file
const htmlPath = path.join(__dirname, 'NPI_API_Documentation.html');
fs.writeFileSync(htmlPath, finalHtml);

console.log('✅ HTML file generated: NPI_API_Documentation.html');
console.log('📄 You can open this file in a browser and use "Print to PDF" to generate the PDF');
console.log('🔗 File location:', htmlPath);

// Also create a simple script to open the file
const openScript = `
@echo off
echo Opening NPI API Documentation in your default browser...
start "" "${htmlPath}"
echo.
echo To generate PDF:
echo 1. Press Ctrl+P (or Cmd+P on Mac)
echo 2. Select "Save as PDF" as destination
echo 3. Choose "More settings" and select "A4" paper size
echo 4. Click "Save"
echo.
pause
`;

fs.writeFileSync(path.join(__dirname, 'open-documentation.bat'), openScript);

console.log('📝 Created helper script: open-documentation.bat');
console.log('💡 Run "open-documentation.bat" to open the documentation and get PDF instructions');
