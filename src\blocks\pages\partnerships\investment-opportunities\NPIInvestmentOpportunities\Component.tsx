'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPI<PERSON>ard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { NPIModal } from '@/components/ui/npi-modal'
import Link from 'next/link'
import Image from 'next/image'
import { TrendingUp, MapPin, Calendar, DollarSign, Download, Filter, Eye, X, ArrowRight } from 'lucide-react'

interface InvestmentOpportunity {
  id: string
  title: string
  description: string
  sector: string
  location: string
  investmentRange: string
  expectedROI: string
  timeline: string
  riskLevel: 'Low' | 'Medium' | 'High'
  stage: 'Concept' | 'Development' | 'Pilot' | 'Scale-up'
  image: string
  keyHighlights: string[]
  marketSize: string
  jobsCreated: number
  communityImpact: string
}

interface NPIInvestmentOpportunitiesProps {
  title?: string
  description?: string
  opportunities?: InvestmentOpportunity[]
}

export const NPIInvestmentOpportunitiesBlock: React.FC<NPIInvestmentOpportunitiesProps> = ({
  title = 'Investment Opportunities',
  description = "Discover high-impact investment opportunities in Kenya's natural products sector. From community-based enterprises to innovative technologies, find projects that deliver both financial returns and social impact.",
  opportunities = [
    {
      id: 'aloe-processing-facility',
      title: 'Aloe Vera Processing Facility Expansion',
      description:
        'Scale up successful aloe vera processing operations in Baringo County with modern equipment and expanded production capacity.',
      sector: 'Natural Products Manufacturing',
      location: 'Baringo County',
      investmentRange: 'KES 50-80M',
      expectedROI: '25-35%',
      timeline: '18-24 months',
      riskLevel: 'Medium',
      stage: 'Scale-up',
      image: '/assets/product 1.jpg',
      keyHighlights: [
        'Proven market demand and existing customer base',
        'Sustainable raw material supply from 500+ farmers',
        'Export potential to East African markets',
        'Strong community ownership and support',
      ],
      marketSize: 'KES 2.5B (East Africa)',
      jobsCreated: 200,
      communityImpact: 'Direct benefit to 1,500 households',
    },
    {
      id: 'moringa-value-chain',
      title: 'Moringa Value Chain Development',
      description:
        'Establish integrated moringa production, processing, and marketing system across Northern Kenya counties.',
      sector: 'Nutrition & Health Products',
      location: 'Turkana, Marsabit, Isiolo',
      investmentRange: 'KES 100-150M',
      expectedROI: '30-40%',
      timeline: '24-36 months',
      riskLevel: 'Medium',
      stage: 'Development',
      image: '/assets/product 2.jpg',
      keyHighlights: [
        'High-nutrition product with global demand',
        'Climate-resilient crop suitable for arid areas',
        'Multiple product streams (powder, oil, capsules)',
        'Strong export potential to US and European markets',
      ],
      marketSize: 'USD 8.5B (Global)',
      jobsCreated: 500,
      communityImpact: 'Improved nutrition for 10,000 children',
    },
    {
      id: 'medicinal-plants-research',
      title: 'Medicinal Plants Research & Development Center',
      description:
        'Establish state-of-the-art research facility for traditional medicine validation and product development.',
      sector: 'Pharmaceutical & Research',
      location: 'Nairobi',
      investmentRange: 'KES 200-300M',
      expectedROI: '20-30%',
      timeline: '36-48 months',
      riskLevel: 'High',
      stage: 'Concept',
      image: '/assets/product 3.jpg',
      keyHighlights: [
        'First-of-its-kind facility in East Africa',
        'Partnership with international pharmaceutical companies',
        'IP development and licensing opportunities',
        'Government support and regulatory backing',
      ],
      marketSize: 'USD 150B (Global traditional medicine)',
      jobsCreated: 150,
      communityImpact: 'Validation of 50+ traditional medicines',
    },
    {
      id: 'honey-processing-cooperative',
      title: 'Community Honey Processing Cooperative',
      description:
        'Support Ogiek community in establishing modern honey processing and packaging facility in Mau Forest.',
      sector: 'Food Processing',
      location: 'Nakuru County',
      investmentRange: 'KES 20-35M',
      expectedROI: '20-25%',
      timeline: '12-18 months',
      riskLevel: 'Low',
      stage: 'Pilot',
      image: '/assets/product 4.jpg',
      keyHighlights: [
        'Sustainable forest conservation model',
        'Premium organic honey with certification potential',
        'Strong community governance structure',
        'Growing demand for natural honey products',
      ],
      marketSize: 'KES 15B (Kenya honey market)',
      jobsCreated: 80,
      communityImpact: 'Forest conservation of 1,000 hectares',
    },
    {
      id: 'textile-fashion-brand',
      title: 'Traditional Textile Fashion Brand',
      description:
        'Scale up traditional textile weaving into contemporary fashion brand with international market reach.',
      sector: 'Fashion & Textiles',
      location: 'Machakos County',
      investmentRange: 'KES 40-60M',
      expectedROI: '25-35%',
      timeline: '18-24 months',
      riskLevel: 'Medium',
      stage: 'Scale-up',
      image: '/assets/product 5.jpg',
      keyHighlights: [
        'Unique cultural designs with modern appeal',
        'Skilled artisan network already established',
        'Growing global demand for authentic African fashion',
        'E-commerce and international retail opportunities',
      ],
      marketSize: 'USD 2.5B (African fashion globally)',
      jobsCreated: 300,
      communityImpact: 'Cultural preservation and women empowerment',
    },
    {
      id: 'essential-oils-distillery',
      title: 'Essential Oils Distillery Network',
      description:
        "Establish network of small-scale essential oil distilleries across Kenya's diverse ecological zones.",
      sector: 'Cosmetics & Aromatherapy',
      location: 'Multiple Counties',
      investmentRange: 'KES 80-120M',
      expectedROI: '30-40%',
      timeline: '24-30 months',
      riskLevel: 'Medium',
      stage: 'Development',
      image: '/assets/product 6.jpg',
      keyHighlights: [
        'High-value products with premium pricing',
        'Diverse plant species across different regions',
        'Growing global aromatherapy and wellness market',
        'Sustainable harvesting and community involvement',
      ],
      marketSize: 'USD 18B (Global essential oils)',
      jobsCreated: 400,
      communityImpact: 'Biodiversity conservation and rural income',
    },
  ],
}) => {
  const [selectedSector, setSelectedSector] = useState('All Sectors')
  const [selectedRisk, setSelectedRisk] = useState('All Risk Levels')
  const [selectedStage, setSelectedStage] = useState('All Stages')
  const [selectedOpportunity, setSelectedOpportunity] = useState<InvestmentOpportunity | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleViewDetails = (opportunity: InvestmentOpportunity) => {
    setSelectedOpportunity(opportunity)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedOpportunity(null)
  }

  const sectors = ['All Sectors', ...Array.from(new Set(opportunities.map((o) => o.sector)))]
  const riskLevels = ['All Risk Levels', 'Low', 'Medium', 'High']
  const stages = ['All Stages', 'Concept', 'Development', 'Pilot', 'Scale-up']

  const filteredOpportunities = opportunities.filter((opportunity) => {
    return (
      (selectedSector === 'All Sectors' || opportunity.sector === selectedSector) &&
      (selectedRisk === 'All Risk Levels' || opportunity.riskLevel === selectedRisk) &&
      (selectedStage === 'All Stages' || opportunity.stage === selectedStage)
    )
  })

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low':
        return 'bg-[#EFE3BA] text-[#725242]'
      case 'Medium':
        return 'bg-[#725242] text-white'
      case 'High':
        return 'bg-[#8A3E25] text-white'
      default:
        return 'bg-[#EFE3BA] text-[#725242]'
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'Concept':
        return 'bg-[#725242] text-white'
      case 'Development':
        return 'bg-[#8A3E25] text-white'
      case 'Pilot':
        return 'bg-[#EFE3BA] text-[#725242]'
      case 'Scale-up':
        return 'bg-[#25718A] text-white'
      default:
        return 'bg-[#EFE3BA] text-[#725242]'
    }
  }

  return (
    <NPISection className="bg-white relative overflow-hidden">
      <NPISectionHeader>
        <NPISectionTitle className="text-black">{title}</NPISectionTitle>
        <NPISectionDescription className="text-black">{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Filters */}
      <NPICard className="mb-8 bg-white border-[#725242]">
        <NPICardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-[#725242]" />
            <span className="font-medium font-npi text-black">Filter Opportunities:</span>
          </div>
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-black">Sector</label>
              <select
                value={selectedSector}
                onChange={(e) => setSelectedSector(e.target.value)}
                className="w-full p-2 border border-[#725242] bg-white text-black focus:outline-none focus:ring-2 focus:ring-[#8A3E25] font-npi"
              >
                {sectors.map((sector) => (
                  <option key={sector} value={sector}>
                    {sector}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-black">
                Risk Level
              </label>
              <select
                value={selectedRisk}
                onChange={(e) => setSelectedRisk(e.target.value)}
                className="w-full p-2 border border-[#725242] bg-white text-black focus:outline-none focus:ring-2 focus:ring-[#8A3E25] font-npi"
              >
                {riskLevels.map((risk) => (
                  <option key={risk} value={risk}>
                    {risk}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-black">Stage</label>
              <select
                value={selectedStage}
                onChange={(e) => setSelectedStage(e.target.value)}
                className="w-full p-2 border border-[#725242] bg-white text-black focus:outline-none focus:ring-2 focus:ring-[#8A3E25] font-npi"
              >
                {stages.map((stage) => (
                  <option key={stage} value={stage}>
                    {stage}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Opportunities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredOpportunities.map((opportunity, index) => {
          // Alternating backgrounds
          const isEven = index % 2 === 0
          const cardBg = isEven ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'

          return (
            <NPICard
              key={opportunity.id}
              className={`overflow-hidden hover:shadow-xl transition-all duration-300 aspect-square flex flex-col ${cardBg} border-2 border-[#725242]/20 hover:border-[#8A3E25] hover:scale-105`}
            >
              {/* Square card header - minimal */}
              <NPICardHeader className="p-3 flex-shrink-0">
                <NPICardTitle className="text-black text-sm leading-tight mb-1 font-bold line-clamp-2">
                  {opportunity.title}
                </NPICardTitle>
                <div className="text-xs text-[#8A3E25] font-medium mb-2">
                  {opportunity.sector}
                </div>
                <div className="flex gap-1 mb-2">
                  <span
                    className={`px-2 py-1 text-xs font-medium ${getRiskColor(opportunity.riskLevel)}`}
                  >
                    {opportunity.riskLevel}
                  </span>
                  <span
                    className={`px-2 py-1 text-xs font-medium ${getStageColor(opportunity.stage)}`}
                  >
                    {opportunity.stage}
                  </span>
                </div>
              </NPICardHeader>

              {/* Minimal content for square cards */}
              <NPICardContent className="p-3 flex-1 flex flex-col justify-between">
                <div className="space-y-2">
                  {/* Essential info only */}
                  <p className="text-[#725242] text-xs font-medium leading-tight line-clamp-2">
                    {opportunity.description}
                  </p>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="text-black font-medium">{opportunity.investmentRange}</span>
                    </div>
                    <div>
                      <span className="text-[#8A3E25] font-medium">{opportunity.expectedROI} ROI</span>
                    </div>
                  </div>
                  <div className="text-xs text-[#725242]/70 font-medium">
                    {opportunity.location} • {opportunity.timeline}
                  </div>
                </div>

                {/* Dynamic action buttons */}
                <div className="mt-3 space-y-2">
                  <NPIButton
                    size="sm"
                    className="w-full bg-[#8A3E25] hover:bg-[#8A3E25]/90 text-white font-semibold py-2 text-xs"
                    onClick={() => handleViewDetails(opportunity)}
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    Learn More
                  </NPIButton>
                  <NPIButton
                    size="sm"
                    variant="outline"
                    className="w-full border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white font-semibold py-2 text-xs"
                    onClick={() => handleViewDetails(opportunity)}
                  >
                    <ArrowRight className="w-3 h-3 mr-1" />
                    Apply Now
                  </NPIButton>
                </div>
              </NPICardContent>
            </NPICard>
          )
        })}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-[#725242] font-npi">
          Showing {filteredOpportunities.length} of {opportunities.length} investment opportunities
        </p>
      </div>

      {/* Investment Opportunity Details Modal */}
      {selectedOpportunity && (
        <NPIModal isOpen={isModalOpen} onClose={handleCloseModal} size="xl" className="bg-white">
          <div className="p-6">
            {/* Modal Header */}
            <div className="flex items-start gap-6 mb-6 bg-[#725242] p-6 text-white -m-6 mb-6">
              <div className="relative w-24 h-24 flex-shrink-0">
                <Image
                  src={selectedOpportunity.image}
                  alt={selectedOpportunity.title}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="flex-1">
                <h2 className="text-3xl font-bold text-white mb-2 font-npi">
                  {selectedOpportunity.title}
                </h2>
                <p className="text-white/90 text-lg mb-2 font-npi">{selectedOpportunity.sector}</p>
                <div className="flex gap-2">
                  <span
                    className={`px-3 py-1 text-sm font-medium ${getRiskColor(selectedOpportunity.riskLevel)}`}
                  >
                    {selectedOpportunity.riskLevel} Risk
                  </span>
                  <span
                    className={`px-3 py-1 text-sm font-medium ${getStageColor(selectedOpportunity.stage)}`}
                  >
                    {selectedOpportunity.stage}
                  </span>
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className="grid md:grid-cols-2 gap-8">
              {/* Left Column */}
              <div>
                <h3 className="text-xl font-bold mb-4 text-[#8A3E25] font-npi">
                  Project Description
                </h3>
                <p className="text-[#725242] mb-6 leading-relaxed font-npi">
                  {selectedOpportunity.description}
                </p>

                <h3 className="text-xl font-bold mb-4 text-[#8A3E25] font-npi">
                  Key Investment Metrics
                </h3>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-[#EFE3BA]/30 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="w-5 h-5 text-[#725242]" />
                      <span className="font-medium text-[#725242] font-npi">Investment Range</span>
                    </div>
                    <div className="text-xl font-bold text-[#8A3E25] font-npi">
                      {selectedOpportunity.investmentRange}
                    </div>
                  </div>
                  <div className="bg-[#EFE3BA]/30 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="w-5 h-5 text-[#725242]" />
                      <span className="font-medium text-[#725242] font-npi">Expected ROI</span>
                    </div>
                    <div className="text-xl font-bold text-[#8A3E25] font-npi">
                      {selectedOpportunity.expectedROI}
                    </div>
                  </div>
                  <div className="bg-[#EFE3BA]/30 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <MapPin className="w-5 h-5 text-[#725242]" />
                      <span className="font-medium text-[#725242] font-npi">Location</span>
                    </div>
                    <div className="text-xl font-bold text-[#8A3E25] font-npi">
                      {selectedOpportunity.location}
                    </div>
                  </div>
                  <div className="bg-[#EFE3BA]/30 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="w-5 h-5 text-[#725242]" />
                      <span className="font-medium text-[#725242] font-npi">Timeline</span>
                    </div>
                    <div className="text-xl font-bold text-[#8A3E25] font-npi">
                      {selectedOpportunity.timeline}
                    </div>
                  </div>
                </div>

                <h3 className="text-xl font-bold mb-4 text-[#8A3E25] font-npi">Key Highlights</h3>
                <ul className="space-y-2 mb-6">
                  {selectedOpportunity.keyHighlights.map((highlight, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-[#8A3E25] rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-[#725242] font-npi leading-relaxed">{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Right Column */}
              <div>
                <h3 className="text-xl font-bold mb-4 text-[#8A3E25] font-npi">
                  Impact & Market Potential
                </h3>
                <div className="bg-[#EFE3BA]/50 p-6 mb-6">
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="text-sm text-[#725242] mb-1 font-npi">Jobs Created</div>
                      <div className="text-2xl font-bold text-[#8A3E25] font-npi">
                        {selectedOpportunity.jobsCreated}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-[#725242] mb-1 font-npi">Market Size</div>
                      <div className="text-2xl font-bold text-[#8A3E25] font-npi">
                        {selectedOpportunity.marketSize}
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-[#725242] mb-2 font-npi">Community Impact</div>
                    <div className="text-lg font-medium text-[#8A3E25] font-npi">
                      {selectedOpportunity.communityImpact}
                    </div>
                  </div>
                </div>

                <h3 className="text-xl font-bold mb-4 text-[#8A3E25] font-npi">Next Steps</h3>
                <div className="space-y-4">
                  <NPIButton
                    asChild
                    className="w-full bg-[#8A3E25] hover:bg-[#8A3E25] text-white py-3"
                    size="lg"
                  >
                    <Link href={`/partnerships/opportunities/${selectedOpportunity.id}`}>
                      View Detailed Project Page
                    </Link>
                  </NPIButton>
                  <NPIButton
                    asChild
                    className="w-full border-2 border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white py-3"
                    variant="outline"
                    size="lg"
                  >
                    <Link
                      href={`/partnerships/opportunities/${selectedOpportunity.id}/prospectus.pdf`}
                    >
                      <Download className="w-5 h-5 mr-2" />
                      Download Investment Prospectus
                    </Link>
                  </NPIButton>
                  <NPIButton
                    asChild
                    className="w-full bg-[#8A3E25] hover:bg-[#8A3E25] text-white py-3"
                    size="lg"
                  >
                    <Link href="/contact">Schedule Investment Consultation</Link>
                  </NPIButton>
                </div>
              </div>
            </div>
          </div>
        </NPIModal>
      )}
    </NPISection>
  )
}
