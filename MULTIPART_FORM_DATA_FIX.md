# Multipart Form Data Fix for Project Creation

## Issue Identified

The JSON parsing error was caused by the Payload CMS admin interface sending data as `multipart/form-data` instead of `application/json`. The API routes were only expecting JSON format, causing parsing failures.

### Error Details
```
Raw request body preview: ------WebKitFormBoundarytvEiUZTa0tpuNjAx
Content-Disposition: form-data; name="_payload"
JSON parsing error: SyntaxError: Unexpected number in JSON at position 1
```

## Root Cause

1. **Content-Type Mismatch**: Payload CMS admin sends `multipart/form-data` with JSON payload in `_payload` field
2. **Array Field Issues**: Empty arrays were being sent as `0` instead of `[]`
3. **API Route Limitations**: Routes only handled `application/json` content type

## Solutions Implemented

### 1. Enhanced API Route Parsing

**Files Modified:**
- `src/app/api/projects/route.ts` (POST and PUT methods)
- `src/app/api/projects/[id]/route.ts` (PUT method)

**Changes:**
- Added content-type detection
- Added multipart/form-data parsing
- Extract JSON from `_payload` field
- Maintain backward compatibility with JSON requests

### 2. Array Field Sanitization

**Problem:** Form data sends empty arrays as `0`
```json
{
  "tags": 0,
  "gallery": 0,
  "impact": {"metrics": 0}
}
```

**Solution:** Convert `0` values to empty arrays `[]`

### 3. Enhanced Validation Hook

**File:** `src/collections/Projects/hooks/validateProjectData.ts`

**Added:**
- Array field sanitization for form data
- Nested object handling
- Robust data structure validation

## Code Changes

### API Route Enhancement
```typescript
// Handle both JSON and multipart form data
const contentType = request.headers.get('content-type') || ''

if (contentType.includes('multipart/form-data')) {
  // Handle multipart form data (from Payload CMS admin)
  const formData = await request.formData()
  const payloadData = formData.get('_payload')
  
  if (payloadData && typeof payloadData === 'string') {
    body = JSON.parse(payloadData)
  }
} else {
  // Handle regular JSON requests
  body = await request.json()
}
```

### Array Field Sanitization
```typescript
// Sanitize array fields that come as 0 from form data
const arrayFields = [
  'tags', 'gallery', 'impact.metrics', 'budget.fundingSources', 
  'resources.links', 'resources.documents', 'timeline.milestones',
  'team.keyPersonnel', 'team.implementingPartners'
]

arrayFields.forEach(fieldPath => {
  // Convert 0 to empty array []
  if (current[finalKey] === 0 || current[finalKey] === '0') {
    current[finalKey] = []
  }
})
```

## Testing the Fix

### 1. Admin Interface Test
1. Navigate to `/admin`
2. Go to "Content Management" > "Projects"
3. Click "Create New"
4. Fill out the form:
   - **Title**: "Test Project"
   - **Summary**: "Test project summary"
   - **Category**: Select any category
   - **Pillar**: Select any pillar
   - **Status**: Select any status
   - **Timeline**: Add start date
   - **Description**: Add rich text content

### 2. Expected Behavior
- ✅ No JSON parsing errors
- ✅ Project saves successfully
- ✅ Array fields handle empty values correctly
- ✅ Rich text content saves properly
- ✅ All validation rules work correctly

### 3. Frontend Verification
1. Navigate to `/projects` page
2. Verify the created project appears
3. Check project details display correctly

## Benefits

1. **Compatibility**: Works with both Payload CMS admin and external API calls
2. **Data Integrity**: Proper array field handling prevents data corruption
3. **Error Prevention**: Comprehensive validation prevents malformed data
4. **User Experience**: Clear error messages for debugging

## Technical Details

### Content-Type Handling
- `multipart/form-data`: Extract from `_payload` field
- `application/json`: Parse directly
- Maintains backward compatibility

### Array Field Mapping
- Handles nested object paths (e.g., `impact.metrics`)
- Converts form data artifacts to proper arrays
- Preserves existing array data

### Validation Flow
1. Parse request based on content-type
2. Sanitize array fields
3. Validate required fields
4. Apply business logic validation
5. Save to database

## Error Handling

### Before Fix
```
JSON parsing error: SyntaxError: Unexpected number in JSON at position 1
```

### After Fix
```
✅ Project created successfully
✅ Data properly validated and sanitized
✅ Arrays correctly formatted
```

This fix ensures that project creation works seamlessly from the Payload CMS admin interface while maintaining compatibility with direct API usage.
